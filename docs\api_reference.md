# Multi-Critic SAC API 参考文档

## 核心模块

### MultiCriticSAC

主要算法类，继承自Stable-Baselines3的BaseAlgorithm。

```python
class MultiCriticSAC(BaseAlgorithm):
    def __init__(
        self,
        policy: Union[str, type] = "MlpPolicy",
        env: Optional[GymEnv] = None,
        learning_rate: float = 3e-4,
        buffer_size: int = 1000000,
        learning_starts: int = 100,
        batch_size: int = 256,
        tau: float = 0.005,
        gamma: float = 0.99,
        critic_weights: Dict[str, float] = None,
        **kwargs
    )
```

**参数:**
- `policy`: 策略类型，通常为"MlpPolicy"
- `env`: 训练环境
- `learning_rate`: 学习率
- `buffer_size`: 经验回放缓冲区大小
- `learning_starts`: 开始学习的步数
- `batch_size`: 批次大小
- `tau`: 软更新参数
- `gamma`: 折扣因子
- `critic_weights`: 评论家权重字典

**主要方法:**

#### learn()
```python
def learn(
    self,
    total_timesteps: int,
    callback=None,
    log_interval: int = 100,
    tb_log_name: str = "MultiCriticSAC",
    reset_num_timesteps: bool = True,
    progress_bar: bool = False,
) -> "MultiCriticSAC"
```

训练模型。

**参数:**
- `total_timesteps`: 总训练步数
- `callback`: 回调函数
- `log_interval`: 日志记录间隔
- `tb_log_name`: TensorBoard日志名称
- `reset_num_timesteps`: 是否重置时间步计数
- `progress_bar`: 是否显示进度条

#### predict()
```python
def predict(
    self,
    observation: np.ndarray,
    state: Optional[Tuple[np.ndarray, ...]] = None,
    episode_start: Optional[np.ndarray] = None,
    deterministic: bool = False,
) -> Tuple[np.ndarray, Optional[Tuple[np.ndarray, ...]]]
```

预测动作。

**参数:**
- `observation`: 输入观测
- `state`: 状态（未使用）
- `episode_start`: 回合开始标志（未使用）
- `deterministic`: 是否使用确定性策略

**返回:**
- `action`: 预测的动作
- `state`: 状态（通常为None）

#### save() / load()
```python
def save(self, path: str) -> None
def load(self, path: str) -> None
```

保存和加载模型。

## 网络模块

### Actor

演员网络，输出连续动作的策略分布。

```python
class Actor(nn.Module):
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        output_activation: str = "tanh",
        log_std_min: float = -20.0,
        log_std_max: float = 2.0,
    )
```

**主要方法:**

#### forward()
```python
def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]
```

前向传播，返回动作均值和对数标准差。

#### sample()
```python
def sample(
    self, 
    state: torch.Tensor, 
    deterministic: bool = False
) -> Tuple[torch.Tensor, torch.Tensor]
```

采样动作，返回动作和对数概率。

#### get_action()
```python
def get_action(self, state: np.ndarray, deterministic: bool = False) -> np.ndarray
```

获取动作（用于环境交互）。

### Critic

评论家网络，估计状态-动作价值函数。

```python
class Critic(nn.Module):
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
    )
```

#### forward()
```python
def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor
```

前向传播，返回Q值。

### DoubleCritic

双评论家网络，包含两个独立的Critic网络。

```python
class DoubleCritic(nn.Module):
    def forward(
        self, 
        state: torch.Tensor, 
        action: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]
```

返回两个Q值。

#### min_q()
```python
def min_q(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor
```

返回两个Q值的最小值。

## 评论家专家模块

### ObstacleExpert

避障专家，专门处理避障相关的价值评估。

```python
class ObstacleExpert(nn.Module):
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        collision_penalty: float = -50.0,
        safety_threshold: float = 2.0,
        distance_scale: float = 10.0,
        **kwargs
    )
```

#### calculate_obstacle_reward()
```python
def calculate_obstacle_reward(
    self,
    state: torch.Tensor,
    action: torch.Tensor,
    next_state: torch.Tensor
) -> torch.Tensor
```

计算避障奖励。

#### update()
```python
def update(self, batch: Dict[str, torch.Tensor], gamma: float = 0.99) -> Dict[str, float]
```

更新网络参数，返回训练指标。

### GuidanceExpert

引导专家，专门处理目标导向的价值评估。

```python
class GuidanceExpert(nn.Module):
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        goal_reward: float = 100.0,
        progress_scale: float = 1.0,
        time_penalty: float = -0.01,
        **kwargs
    )
```

#### calculate_guidance_reward()
```python
def calculate_guidance_reward(
    self,
    state: torch.Tensor,
    action: torch.Tensor,
    next_state: torch.Tensor
) -> torch.Tensor
```

计算引导奖励。

### EnvironmentExpert

环境影响专家，处理环境因素的价值评估。

```python
class EnvironmentExpert(nn.Module):
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        current_scale: float = 0.5,
        wind_scale: float = 0.3,
        energy_penalty_scale: float = 0.1,
        **kwargs
    )
```

#### calculate_environment_reward()
```python
def calculate_environment_reward(
    self,
    state: torch.Tensor,
    action: torch.Tensor,
    next_state: torch.Tensor
) -> torch.Tensor
```

计算环境影响奖励。

### FusionModule

融合模块，整合多个专家的输出。

```python
class FusionModule(nn.Module):
    def __init__(
        self,
        obstacle_expert: ObstacleExpert,
        guidance_expert: GuidanceExpert,
        environment_expert: EnvironmentExpert,
        fusion_weights: Dict[str, float] = None,
        fusion_strategy: str = "weighted_sum",
        adaptive_weights: bool = False,
    )
```

#### forward()
```python
def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor
```

融合所有专家的Q值。

#### get_fusion_weights()
```python
def get_fusion_weights(
    self, 
    state: torch.Tensor, 
    action: torch.Tensor
) -> Dict[str, torch.Tensor]
```

获取当前融合权重。

## 环境模块

### BaseEnv

基础环境类，定义标准接口。

```python
class BaseEnv(gym.Env):
    def __init__(
        self,
        max_episode_steps: int = 1000,
        render_mode: Optional[str] = None,
        seed: Optional[int] = None
    )
```

**抽象方法（需要子类实现）:**

#### _get_observation()
```python
@abstractmethod
def _get_observation(self) -> np.ndarray
```

返回44维观测向量。

#### _calculate_reward()
```python
@abstractmethod
def _calculate_reward(self, action: np.ndarray) -> Dict[str, float]
```

计算奖励，返回包含总奖励和分解奖励的字典。

#### _is_terminated()
```python
@abstractmethod
def _is_terminated(self) -> bool
```

检查是否终止。

#### _execute_action()
```python
@abstractmethod
def _execute_action(self, action: np.ndarray) -> None
```

执行动作。

### NavigationEnv

具体的导航环境实现。

```python
class NavigationEnv(BaseEnv):
    def __init__(
        self,
        map_size: float = 100.0,
        num_obstacles: int = 10,
        obstacle_radius_range: Tuple[float, float] = (2.0, 5.0),
        goal_threshold: float = 1.0,
        **kwargs
    )
```

#### get_environment_info()
```python
def get_environment_info(self) -> Dict[str, Any]
```

获取环境状态信息。

## 工具模块

### ReplayBuffer

经验回放缓冲区。

```python
class ReplayBuffer:
    def __init__(
        self,
        capacity: int,
        state_dim: int,
        action_dim: int,
        device: torch.device = torch.device("cpu"),
        n_envs: int = 1
    )
```

#### add()
```python
def add(
    self,
    state: np.ndarray,
    action: np.ndarray,
    reward: float,
    next_state: np.ndarray,
    done: bool,
    obstacle_reward: float = 0.0,
    guidance_reward: float = 0.0,
    environment_reward: float = 0.0
) -> None
```

添加经验。

#### sample()
```python
def sample(self, batch_size: int) -> Dict[str, torch.Tensor]
```

采样批次数据。

### Logger

日志记录器。

```python
class Logger:
    def __init__(
        self,
        log_dir: str = "./logs/",
        experiment_name: Optional[str] = None,
        use_tensorboard: bool = True,
        use_wandb: bool = False,
    )
```

#### log_scalar()
```python
def log_scalar(self, tag: str, value: float, step: Optional[int] = None) -> None
```

记录标量值。

#### log_episode_metrics()
```python
def log_episode_metrics(self, episode: int, metrics: Dict[str, float]) -> None
```

记录回合指标。

## 配置模块

### Config

配置管理类。

```python
@dataclass
class Config:
    algorithm: AlgorithmConfig
    critic_weights: CriticWeightsConfig
    network: NetworkConfig
    environment: EnvironmentConfig
    training: TrainingConfig
    rewards: RewardsConfig
    logging: LoggingConfig
    device: DeviceConfig
    seed: int = 42
```

#### load_config()
```python
def load_config(config_path: Union[str, Path]) -> Config
```

从YAML文件加载配置。

#### save_config()
```python
def save_config(config: Config, config_path: Union[str, Path]) -> None
```

保存配置到YAML文件。

#### validate_config()
```python
def validate_config(config: Config) -> bool
```

验证配置有效性。

## 可视化模块

### TrainingVisualizer

训练过程可视化器。

```python
class TrainingVisualizer:
    def plot_training_curves(
        self, 
        log_data: Dict[str, List[float]], 
        save_path: Optional[str] = None
    ) -> None
```

绘制训练曲线。

#### plot_critic_weights_evolution()
```python
def plot_critic_weights_evolution(
    self, 
    weights_data: Dict[str, List[float]], 
    save_path: Optional[str] = None
) -> None
```

绘制评论家权重演化。

### EnvironmentVisualizer

环境可视化器。

```python
class EnvironmentVisualizer:
    def plot_trajectory(
        self,
        trajectory: List[Tuple[float, float]],
        obstacles: List[Tuple[float, float, float]],
        goal_pos: Tuple[float, float],
        save_path: Optional[str] = None
    ) -> None
```

绘制智能体轨迹。

## 常用工具函数

### 通用工具

```python
def set_seed(seed: int) -> None
```

设置随机种子。

```python
def get_device(use_cuda: bool = True, cuda_device: int = 0) -> torch.device
```

获取计算设备。

```python
def soft_update(target_net: nn.Module, source_net: nn.Module, tau: float) -> None
```

软更新目标网络。

```python
def normalize_angle(angle: float) -> float
```

归一化角度到[-π, π]。

```python
def calculate_distance(pos1: np.ndarray, pos2: np.ndarray) -> float
```

计算欧几里得距离。
