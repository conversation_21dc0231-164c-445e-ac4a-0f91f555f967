# Multi-Critic SAC 默认配置文件

# 算法参数
algorithm:
  name: "MultiCriticSAC"
  learning_rate: 3.0e-4
  buffer_size: 1000000
  learning_starts: 100
  batch_size: 256
  tau: 0.005  # 软更新参数
  gamma: 0.99  # 折扣因子
  train_freq: 1
  gradient_steps: 1
  ent_coef: "auto"  # 熵系数
  target_update_interval: 1
  target_entropy: "auto"

# 评论家权重配置
critic_weights:
  obstacle: 0.4      # 避障专家权重
  guidance: 0.4      # 引导专家权重  
  environment: 0.2   # 环境影响专家权重

# 网络架构
network:
  policy_kwargs:
    net_arch: [256, 256]  # 网络层大小
    activation_fn: "relu"
  
  # Actor网络配置
  actor:
    hidden_sizes: [256, 256]
    activation: "relu"
    output_activation: "tanh"
  
  # Critic网络配置  
  critic:
    hidden_sizes: [256, 256]
    activation: "relu"
    n_critics: 2  # 每个专家组的critic数量

# 环境配置
environment:
  name: "NavigationEnv"
  max_episode_steps: 1000
  
  # 状态空间配置 (44+维)
  state_space:
    radar_points: 36      # 雷达数据点数 (360°/10°)
    target_vector: 2      # 目标指向向量
    self_state: 2         # 自身状态 (速度, 角速度)
    environment_data: 4   # 环境数据 (洋流x,y, 风x,y)
    
  # 动作空间配置
  action_space:
    type: "continuous"
    low: [-1.0, -1.0]     # [线速度, 角速度] 最小值
    high: [1.0, 1.0]      # [线速度, 角速度] 最大值

# 训练配置
training:
  total_timesteps: 1000000
  eval_freq: 10000
  n_eval_episodes: 10
  eval_log_path: "./logs/eval/"
  
  # 多进程配置
  n_envs: 4  # 并行环境数量
  vec_env_type: "dummy"  # "dummy" 或 "subproc"

# 奖励函数配置
rewards:
  # 避障奖励
  obstacle:
    collision_penalty: -50.0    # 碰撞惩罚
    safety_threshold: 2.0       # 安全距离阈值
    distance_scale: 10.0        # 距离奖励缩放
    
  # 引导奖励  
  guidance:
    goal_reward: 100.0          # 到达目标奖励
    progress_scale: 1.0         # 进度奖励缩放
    time_penalty: -0.01         # 时间惩罚
    
  # 环境奖励
  environment:
    current_scale: 0.5          # 洋流影响缩放
    wind_scale: 0.3             # 风力影响缩放

# 日志和监控
logging:
  tensorboard_log: "./logs/tensorboard/"
  log_interval: 100
  save_freq: 50000
  model_save_path: "./models/"
  
  # 记录的指标
  metrics:
    - "total_reward"
    - "obstacle_reward" 
    - "guidance_reward"
    - "environment_reward"
    - "actor_loss"
    - "critic_loss"
    - "episode_length"

# 设备配置
device:
  use_cuda: true
  cuda_device: 0

# 随机种子
seed: 42
