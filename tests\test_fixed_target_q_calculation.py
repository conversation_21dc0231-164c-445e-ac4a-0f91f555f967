"""
测试修复后的目标Q值计算

验证所有专家网络现在正确使用Actor网络从下一状态采样动作来计算目标Q值，
符合SAC算法的标准实现。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from multi_critic_sac.critics.obstacle_expert import <PERSON><PERSON><PERSON><PERSON><PERSON>x<PERSON>
from multi_critic_sac.critics.guidance_expert import <PERSON>uidanceExpert
from multi_critic_sac.critics.environment_expert import EnvironmentExpert
from multi_critic_sac.networks.actor import Actor


def test_fixed_target_q_calculation():
    """测试修复后的目标Q值计算"""
    print("开始测试修复后的目标Q值计算...")
    
    try:
        device = torch.device("cpu")
        batch_size = 4
        state_dim = 45
        action_dim = 2
        
        # 创建Actor网络
        actor = Actor(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=[256, 256],
            activation="relu"
        ).to(device)
        
        # 创建专家网络
        obstacle_expert = ObstacleExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        guidance_expert = GuidanceExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        environment_expert = EnvironmentExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        print("测试1: 验证专家网络update方法需要Actor参数")
        
        # 创建测试数据
        states = torch.randn(batch_size, state_dim, device=device)
        actions = torch.randn(batch_size, action_dim, device=device)
        next_states = torch.randn(batch_size, state_dim, device=device)
        dones = torch.zeros(batch_size, dtype=torch.bool, device=device)
        
        batch = {
            'states': states,
            'actions': actions,
            'rewards': torch.randn(batch_size),
            'next_states': next_states,
            'dones': dones,
            'obstacle_rewards': torch.randn(batch_size),
            'guidance_rewards': torch.randn(batch_size),
            'environment_rewards': torch.randn(batch_size)
        }
        
        # 测试没有Actor参数时应该抛出错误
        try:
            obstacle_expert.update(batch, gamma=0.99, actor=None)
            assert False, "应该抛出ValueError"
        except ValueError as e:
            assert "Actor网络不能为None" in str(e)
            print("✓ 避障专家正确验证Actor参数")
        
        try:
            guidance_expert.update(batch, gamma=0.99, actor=None)
            assert False, "应该抛出ValueError"
        except ValueError as e:
            assert "Actor网络不能为None" in str(e)
            print("✓ 引导专家正确验证Actor参数")
        
        try:
            environment_expert.update(batch, gamma=0.99, actor=None)
            assert False, "应该抛出ValueError"
        except ValueError as e:
            assert "Actor网络不能为None" in str(e)
            print("✓ 环境影响专家正确验证Actor参数")
        
        print("测试2: 验证专家网络使用Actor正确计算目标Q值")
        
        # 测试使用Actor网络的正确更新
        obstacle_metrics = obstacle_expert.update(batch, gamma=0.99, actor=actor)
        assert 'obstacle_total_loss' in obstacle_metrics
        print("✓ 避障专家使用Actor网络成功更新")
        
        guidance_metrics = guidance_expert.update(batch, gamma=0.99, actor=actor)
        assert 'guidance_total_loss' in guidance_metrics
        print("✓ 引导专家使用Actor网络成功更新")
        
        environment_metrics = environment_expert.update(batch, gamma=0.99, actor=actor)
        assert 'environment_total_loss' in environment_metrics
        print("✓ 环境影响专家使用Actor网络成功更新")
        
        print("测试3: 验证Actor采样的一致性")
        
        # 验证Actor能够从状态采样动作
        with torch.no_grad():
            sampled_actions, log_probs = actor.sample(next_states)
            assert sampled_actions.shape == (batch_size, action_dim)
            assert log_probs.shape == (batch_size, 1)
            print("✓ Actor网络采样功能正常")
        
        print("测试4: 验证目标Q值计算的数值合理性")
        
        # 多次运行更新，检查损失是否在合理范围内
        for i in range(3):
            obstacle_metrics = obstacle_expert.update(batch, gamma=0.99, actor=actor)
            guidance_metrics = guidance_expert.update(batch, gamma=0.99, actor=actor)
            environment_metrics = environment_expert.update(batch, gamma=0.99, actor=actor)
            
            # 检查损失值是否为有限数值
            assert np.isfinite(obstacle_metrics['obstacle_total_loss'])
            assert np.isfinite(guidance_metrics['guidance_total_loss'])
            assert np.isfinite(environment_metrics['environment_total_loss'])
        
        print("✓ 目标Q值计算数值稳定")
        
        print("测试5: 验证不同批次的一致性")
        
        # 创建另一个批次测试
        batch2 = {
            'states': torch.randn(batch_size, state_dim, device=device),
            'actions': torch.randn(batch_size, action_dim, device=device),
            'rewards': torch.randn(batch_size),
            'next_states': torch.randn(batch_size, state_dim, device=device),
            'dones': torch.zeros(batch_size, dtype=torch.bool, device=device),
            'obstacle_rewards': torch.randn(batch_size),
            'guidance_rewards': torch.randn(batch_size),
            'environment_rewards': torch.randn(batch_size)
        }
        
        # 测试不同批次的更新
        obstacle_metrics2 = obstacle_expert.update(batch2, gamma=0.99, actor=actor)
        guidance_metrics2 = guidance_expert.update(batch2, gamma=0.99, actor=actor)
        environment_metrics2 = environment_expert.update(batch2, gamma=0.99, actor=actor)
        
        # 验证返回的指标结构一致
        assert set(obstacle_metrics.keys()) == set(obstacle_metrics2.keys())
        assert set(guidance_metrics.keys()) == set(guidance_metrics2.keys())
        assert set(environment_metrics.keys()) == set(environment_metrics2.keys())
        
        print("✓ 不同批次处理一致")
        
        print("\n🎉 所有测试通过！目标Q值计算修复成功。")
        
        # 打印修复总结
        print("\n📋 修复总结:")
        print("1. ✅ 所有专家网络现在需要Actor参数")
        print("2. ✅ 目标Q值计算使用Actor从下一状态采样动作")
        print("3. ✅ 符合SAC算法的标准实现")
        print("4. ✅ 数值计算稳定可靠")
        print("5. ✅ 错误处理机制完善")
        
        print("\n🔧 修复的关键变化:")
        print("- update方法签名: 添加了actor参数")
        print("- 目标Q值计算: next_actions, _ = actor.sample(next_states)")
        print("- 主训练循环: 传递self.actor给专家更新方法")
        print("- 类型提示: 添加了Actor类型的TYPE_CHECKING导入")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_target_q_calculation()
    sys.exit(0 if success else 1)
