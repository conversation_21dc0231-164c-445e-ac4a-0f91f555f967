#!/usr/bin/env python3
"""
Multi-Critic SAC训练脚本

使用配置文件训练Multi-Critic SAC模型。
支持多进程环境、模型保存、日志记录等功能。

使用方法:
    python scripts/train.py --config configs/default.yaml
    python scripts/train.py --config configs/default.yaml --resume models/checkpoint.pt

作者: Multi-Critic SAC Team
"""

import argparse
import os
import sys
from pathlib import Path
import numpy as np
import torch
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.callbacks import EvalCallback, CheckpointCallback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from multi_critic_sac.config import load_config, validate_config
from multi_critic_sac.utils.common import set_seed, create_directories, setup_logger


def make_env(env_config: dict, rank: int = 0, seed: int = 0):
    """
    创建环境的工厂函数
    
    Args:
        env_config: 环境配置
        rank: 进程排名
        seed: 随机种子
        
    Returns:
        callable: 环境创建函数
    """
    def _init():
        env = NavigationEnv(
            max_episode_steps=env_config.get('max_episode_steps', 1000),
            seed=seed + rank
        )
        env = Monitor(env)
        return env
    
    return _init


def create_vectorized_env(config, n_envs: int = 1, vec_env_type: str = "dummy"):
    """
    创建向量化环境
    
    Args:
        config: 配置对象
        n_envs: 环境数量
        vec_env_type: 向量化环境类型
        
    Returns:
        VecEnv: 向量化环境
    """
    env_config = config.environment.__dict__
    
    # 创建环境列表
    env_fns = [make_env(env_config, i, config.seed) for i in range(n_envs)]
    
    # 选择向量化环境类型
    if vec_env_type == "subproc" and n_envs > 1:
        vec_env = SubprocVecEnv(env_fns)
        print(f"使用SubprocVecEnv，{n_envs}个并行环境")
    else:
        vec_env = DummyVecEnv(env_fns)
        print(f"使用DummyVecEnv，{n_envs}个环境")
    
    return vec_env


def setup_callbacks(config, model_save_path: str, eval_env):
    """
    设置训练回调函数
    
    Args:
        config: 配置对象
        model_save_path: 模型保存路径
        eval_env: 评估环境
        
    Returns:
        list: 回调函数列表
    """
    callbacks = []
    
    # 评估回调
    if eval_env is not None:
        eval_callback = EvalCallback(
            eval_env,
            best_model_save_path=os.path.join(model_save_path, "best_model"),
            log_path=config.logging.eval_log_path,
            eval_freq=config.training.eval_freq,
            n_eval_episodes=config.training.n_eval_episodes,
            deterministic=True,
            render=False,
            verbose=1
        )
        callbacks.append(eval_callback)
    
    # 检查点回调
    checkpoint_callback = CheckpointCallback(
        save_freq=config.logging.save_freq,
        save_path=model_save_path,
        name_prefix="checkpoint"
    )
    callbacks.append(checkpoint_callback)
    
    return callbacks


def train_model(config_path: str, resume_path: str = None, 
               output_dir: str = None, experiment_name: str = None):
    """
    训练Multi-Critic SAC模型
    
    Args:
        config_path: 配置文件路径
        resume_path: 恢复训练的模型路径
        output_dir: 输出目录
        experiment_name: 实验名称
    """
    # 加载配置
    config = load_config(config_path)
    validate_config(config)
    
    # 设置随机种子
    set_seed(config.seed)
    
    # 设置输出目录
    if output_dir is None:
        output_dir = "outputs"
    
    if experiment_name is None:
        from datetime import datetime
        experiment_name = f"multi_critic_sac_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 创建目录
    experiment_dir = Path(output_dir) / experiment_name
    model_save_path = experiment_dir / "models"
    log_path = experiment_dir / "logs"
    
    create_directories([experiment_dir, model_save_path, log_path])
    
    # 设置日志
    logger = setup_logger(
        "MultiCriticSAC_Training",
        log_file=log_path / "training.log"
    )
    
    logger.info(f"开始训练实验: {experiment_name}")
    logger.info(f"配置文件: {config_path}")
    logger.info(f"输出目录: {experiment_dir}")
    
    # 创建训练环境
    train_env = create_vectorized_env(
        config,
        n_envs=config.training.n_envs,
        vec_env_type=config.training.vec_env_type
    )
    
    # 创建评估环境
    eval_env = create_vectorized_env(config, n_envs=1, vec_env_type="dummy")
    
    # 创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=train_env,
        learning_rate=config.algorithm.learning_rate,
        buffer_size=config.algorithm.buffer_size,
        learning_starts=config.algorithm.learning_starts,
        batch_size=config.algorithm.batch_size,
        tau=config.algorithm.tau,
        gamma=config.algorithm.gamma,
        train_freq=config.algorithm.train_freq,
        gradient_steps=config.algorithm.gradient_steps,
        ent_coef=config.algorithm.ent_coef,
        target_update_interval=config.algorithm.target_update_interval,
        target_entropy=config.algorithm.target_entropy,
        critic_weights=config.critic_weights.__dict__,
        tensorboard_log=str(log_path / "tensorboard"),
        verbose=1,
        seed=config.seed,
        config=config
    )
    
    logger.info("模型创建完成")
    logger.info(f"训练参数: {model.get_training_stats()}")
    
    # 恢复训练（如果指定）
    if resume_path and os.path.exists(resume_path):
        logger.info(f"从检查点恢复训练: {resume_path}")
        model.load(resume_path)
    
    # 设置回调函数
    callbacks = setup_callbacks(config, str(model_save_path), eval_env)
    
    # 开始训练
    logger.info("开始训练...")
    try:
        model.learn(
            total_timesteps=config.training.total_timesteps,
            callback=callbacks,
            log_interval=config.logging.log_interval,
            tb_log_name="MultiCriticSAC"
        )
        
        logger.info("训练完成")
        
        # 保存最终模型
        final_model_path = model_save_path / "final_model.pt"
        model.save(str(final_model_path))
        logger.info(f"最终模型已保存: {final_model_path}")
        
        # 保存配置
        config_save_path = experiment_dir / "config.yaml"
        from multi_critic_sac.config import save_config
        save_config(config, config_save_path)
        logger.info(f"配置已保存: {config_save_path}")
        
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
        # 保存中断时的模型
        interrupt_model_path = model_save_path / "interrupted_model.pt"
        model.save(str(interrupt_model_path))
        logger.info(f"中断模型已保存: {interrupt_model_path}")
    
    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        raise
    
    finally:
        # 关闭环境
        train_env.close()
        eval_env.close()
        
        # 关闭日志
        if hasattr(model, 'logger') and model.logger:
            model.logger.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Multi-Critic SAC训练脚本")
    parser.add_argument(
        "--config", "-c",
        type=str,
        required=True,
        help="配置文件路径"
    )
    parser.add_argument(
        "--resume", "-r",
        type=str,
        default=None,
        help="恢复训练的模型路径"
    )
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="outputs",
        help="输出目录"
    )
    parser.add_argument(
        "--experiment-name", "-n",
        type=str,
        default=None,
        help="实验名称"
    )
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 开始训练
    train_model(
        config_path=args.config,
        resume_path=args.resume,
        output_dir=args.output_dir,
        experiment_name=args.experiment_name
    )


if __name__ == "__main__":
    main()
