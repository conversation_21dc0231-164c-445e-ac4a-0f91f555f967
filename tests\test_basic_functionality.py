#!/usr/bin/env python3
"""
Multi-Critic SAC基础功能测试

测试项目的核心功能是否正常工作，包括：
- 环境创建和基本操作
- 模型创建和训练
- 配置系统
- 网络模块
- 工具函数

作者: Multi-Critic SAC Team
"""

import unittest
import sys
import tempfile
import shutil
from pathlib import Path
import numpy as np
import torch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv, BaseEnv
from multi_critic_sac.config import get_default_config, load_config, save_config, validate_config
from multi_critic_sac.networks import Actor, Critic
from multi_critic_sac.critics import ObstacleExpert, GuidanceExpert, EnvironmentExpert, FusionModule
from multi_critic_sac.utils import ReplayBuffer, Logger
from multi_critic_sac.utils.common import set_seed, get_device, normalize_angle


class TestEnvironment(unittest.TestCase):
    """测试环境模块"""
    
    def setUp(self):
        """测试前准备"""
        self.env = NavigationEnv(
            map_size=50.0,
            num_obstacles=3,
            max_episode_steps=100,
            seed=42
        )
    
    def tearDown(self):
        """测试后清理"""
        self.env.close()
    
    def test_environment_creation(self):
        """测试环境创建"""
        self.assertIsInstance(self.env, BaseEnv)
        self.assertEqual(self.env.observation_space.shape[0], 44)
        self.assertEqual(self.env.action_space.shape[0], 2)
    
    def test_environment_reset(self):
        """测试环境重置"""
        obs, info = self.env.reset()
        self.assertEqual(obs.shape, (44,))
        self.assertIsInstance(info, dict)
    
    def test_environment_step(self):
        """测试环境步进"""
        obs, _ = self.env.reset()
        action = self.env.action_space.sample()
        
        new_obs, reward, terminated, truncated, info = self.env.step(action)
        
        self.assertEqual(new_obs.shape, (44,))
        self.assertIsInstance(reward, (int, float))
        self.assertIsInstance(terminated, bool)
        self.assertIsInstance(truncated, bool)
        self.assertIsInstance(info, dict)
        
        # 检查分解奖励
        self.assertIn('obstacle_reward', info)
        self.assertIn('guidance_reward', info)
        self.assertIn('environment_reward', info)
    
    def test_environment_info(self):
        """测试环境信息获取"""
        self.env.reset()
        env_info = self.env.get_environment_info()
        
        required_keys = [
            'agent_position', 'goal_position', 'distance_to_goal',
            'current_vector', 'wind_vector', 'min_obstacle_distance'
        ]
        
        for key in required_keys:
            self.assertIn(key, env_info)


class TestNetworks(unittest.TestCase):
    """测试网络模块"""
    
    def setUp(self):
        """测试前准备"""
        self.state_dim = 44
        self.action_dim = 2
        self.device = torch.device("cpu")
    
    def test_actor_creation(self):
        """测试Actor网络创建"""
        actor = Actor(self.state_dim, self.action_dim)
        self.assertIsInstance(actor, torch.nn.Module)
        
        # 测试参数数量
        param_count = actor.get_parameters_count()
        self.assertGreater(param_count, 0)
    
    def test_actor_forward(self):
        """测试Actor前向传播"""
        actor = Actor(self.state_dim, self.action_dim)
        state = torch.randn(1, self.state_dim)
        
        mean, log_std = actor.forward(state)
        
        self.assertEqual(mean.shape, (1, self.action_dim))
        self.assertEqual(log_std.shape, (1, self.action_dim))
    
    def test_actor_sample(self):
        """测试Actor动作采样"""
        actor = Actor(self.state_dim, self.action_dim)
        state = torch.randn(1, self.state_dim)
        
        # 随机采样
        action, log_prob = actor.sample(state, deterministic=False)
        self.assertEqual(action.shape, (1, self.action_dim))
        self.assertEqual(log_prob.shape, (1, 1))
        
        # 确定性采样
        action_det, log_prob_det = actor.sample(state, deterministic=True)
        self.assertEqual(action_det.shape, (1, self.action_dim))
        self.assertIsNone(log_prob_det)
    
    def test_critic_creation(self):
        """测试Critic网络创建"""
        critic = Critic(self.state_dim, self.action_dim)
        self.assertIsInstance(critic, torch.nn.Module)
    
    def test_critic_forward(self):
        """测试Critic前向传播"""
        critic = Critic(self.state_dim, self.action_dim)
        state = torch.randn(1, self.state_dim)
        action = torch.randn(1, self.action_dim)
        
        q_value = critic.forward(state, action)
        self.assertEqual(q_value.shape, (1, 1))


class TestCriticExperts(unittest.TestCase):
    """测试评论家专家模块"""
    
    def setUp(self):
        """测试前准备"""
        self.state_dim = 44
        self.action_dim = 2
        self.device = torch.device("cpu")
    
    def test_obstacle_expert_creation(self):
        """测试避障专家创建"""
        expert = ObstacleExpert(self.state_dim, self.action_dim, device=self.device)
        self.assertIsInstance(expert, torch.nn.Module)
    
    def test_guidance_expert_creation(self):
        """测试引导专家创建"""
        expert = GuidanceExpert(self.state_dim, self.action_dim, device=self.device)
        self.assertIsInstance(expert, torch.nn.Module)
    
    def test_environment_expert_creation(self):
        """测试环境专家创建"""
        expert = EnvironmentExpert(self.state_dim, self.action_dim, device=self.device)
        self.assertIsInstance(expert, torch.nn.Module)
    
    def test_fusion_module_creation(self):
        """测试融合模块创建"""
        obstacle_expert = ObstacleExpert(self.state_dim, self.action_dim, device=self.device)
        guidance_expert = GuidanceExpert(self.state_dim, self.action_dim, device=self.device)
        environment_expert = EnvironmentExpert(self.state_dim, self.action_dim, device=self.device)
        
        fusion = FusionModule(
            obstacle_expert=obstacle_expert,
            guidance_expert=guidance_expert,
            environment_expert=environment_expert,
            device=self.device
        )
        
        self.assertIsInstance(fusion, torch.nn.Module)
    
    def test_fusion_module_forward(self):
        """测试融合模块前向传播"""
        obstacle_expert = ObstacleExpert(self.state_dim, self.action_dim, device=self.device)
        guidance_expert = GuidanceExpert(self.state_dim, self.action_dim, device=self.device)
        environment_expert = EnvironmentExpert(self.state_dim, self.action_dim, device=self.device)
        
        fusion = FusionModule(
            obstacle_expert=obstacle_expert,
            guidance_expert=guidance_expert,
            environment_expert=environment_expert,
            device=self.device
        )
        
        state = torch.randn(1, self.state_dim)
        action = torch.randn(1, self.action_dim)
        
        fused_q = fusion.forward(state, action)
        self.assertEqual(fused_q.shape, (1, 1))


class TestReplayBuffer(unittest.TestCase):
    """测试经验回放缓冲区"""
    
    def setUp(self):
        """测试前准备"""
        self.buffer = ReplayBuffer(
            capacity=1000,
            state_dim=44,
            action_dim=2,
            device=torch.device("cpu")
        )
    
    def test_buffer_creation(self):
        """测试缓冲区创建"""
        self.assertEqual(self.buffer.capacity, 1000)
        self.assertEqual(self.buffer.state_dim, 44)
        self.assertEqual(self.buffer.action_dim, 2)
        self.assertEqual(len(self.buffer), 0)
    
    def test_buffer_add(self):
        """测试添加经验"""
        state = np.random.randn(44)
        action = np.random.randn(2)
        reward = 1.0
        next_state = np.random.randn(44)
        done = False
        
        self.buffer.add(state, action, reward, next_state, done)
        self.assertEqual(len(self.buffer), 1)
    
    def test_buffer_sample(self):
        """测试采样"""
        # 添加一些经验
        for _ in range(100):
            state = np.random.randn(44)
            action = np.random.randn(2)
            reward = np.random.randn()
            next_state = np.random.randn(44)
            done = np.random.choice([True, False])
            
            self.buffer.add(state, action, reward, next_state, done)
        
        # 采样
        batch = self.buffer.sample(32)
        
        self.assertIn('states', batch)
        self.assertIn('actions', batch)
        self.assertIn('rewards', batch)
        self.assertIn('next_states', batch)
        self.assertIn('dones', batch)
        
        self.assertEqual(batch['states'].shape, (32, 44))
        self.assertEqual(batch['actions'].shape, (32, 2))


class TestConfig(unittest.TestCase):
    """测试配置系统"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_default_config(self):
        """测试默认配置"""
        config = get_default_config()
        self.assertIsNotNone(config)
        
        # 验证配置
        self.assertTrue(validate_config(config))
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        config = get_default_config()
        config_path = Path(self.temp_dir) / "test_config.yaml"
        
        # 保存配置
        save_config(config, config_path)
        self.assertTrue(config_path.exists())
        
        # 加载配置
        loaded_config = load_config(config_path)
        
        # 验证配置内容
        self.assertEqual(config.algorithm.learning_rate, loaded_config.algorithm.learning_rate)
        self.assertEqual(config.critic_weights.obstacle, loaded_config.critic_weights.obstacle)


class TestMultiCriticSAC(unittest.TestCase):
    """测试Multi-Critic SAC算法"""
    
    def setUp(self):
        """测试前准备"""
        self.env = NavigationEnv(
            map_size=30.0,
            num_obstacles=2,
            max_episode_steps=50,
            seed=42
        )
    
    def tearDown(self):
        """测试后清理"""
        self.env.close()
    
    def test_model_creation(self):
        """测试模型创建"""
        model = MultiCriticSAC(
            policy="MlpPolicy",
            env=self.env,
            learning_starts=10,
            verbose=0
        )
        
        self.assertIsInstance(model, MultiCriticSAC)
    
    def test_model_predict(self):
        """测试模型预测"""
        model = MultiCriticSAC(
            policy="MlpPolicy",
            env=self.env,
            learning_starts=10,
            verbose=0
        )
        
        obs, _ = self.env.reset()
        action, _ = model.predict(obs)
        
        self.assertEqual(action.shape, (2,))
        self.assertTrue(np.all(action >= -1.0))
        self.assertTrue(np.all(action <= 1.0))
    
    def test_model_learn(self):
        """测试模型学习"""
        model = MultiCriticSAC(
            policy="MlpPolicy",
            env=self.env,
            learning_starts=10,
            verbose=0
        )
        
        # 短期训练
        model.learn(total_timesteps=50)
        
        # 检查训练统计
        stats = model.get_training_stats()
        self.assertGreater(stats['num_timesteps'], 0)


class TestUtilities(unittest.TestCase):
    """测试工具函数"""
    
    def test_set_seed(self):
        """测试随机种子设置"""
        set_seed(42)
        
        # 测试numpy随机性
        a1 = np.random.randn(10)
        set_seed(42)
        a2 = np.random.randn(10)
        
        np.testing.assert_array_equal(a1, a2)
    
    def test_get_device(self):
        """测试设备获取"""
        device = get_device(use_cuda=False)
        self.assertEqual(device.type, "cpu")
    
    def test_normalize_angle(self):
        """测试角度归一化"""
        # 测试正常角度
        self.assertAlmostEqual(normalize_angle(0.5), 0.5)
        
        # 测试大于π的角度
        self.assertAlmostEqual(normalize_angle(4.0), 4.0 - 2*np.pi, places=5)
        
        # 测试小于-π的角度
        self.assertAlmostEqual(normalize_angle(-4.0), -4.0 + 2*np.pi, places=5)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestEnvironment,
        TestNetworks,
        TestCriticExperts,
        TestReplayBuffer,
        TestConfig,
        TestMultiCriticSAC,
        TestUtilities
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("Multi-Critic SAC 基础功能测试")
    print("="*50)
    
    success = run_tests()
    
    if success:
        print("\n所有测试通过！✅")
        sys.exit(0)
    else:
        print("\n部分测试失败！❌")
        sys.exit(1)
