"""
配置管理模块

提供配置文件加载、验证和管理功能。
支持YAML格式的配置文件，并提供类型安全的配置访问。

作者: Multi-Critic SAC Team
"""

import os
import yaml
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class AlgorithmConfig:
    """算法配置类"""
    name: str = "MultiCriticSAC"
    learning_rate: float = 3e-4
    buffer_size: int = 1000000
    learning_starts: int = 100
    batch_size: int = 256
    tau: float = 0.005
    gamma: float = 0.99
    train_freq: int = 1
    gradient_steps: int = 1
    ent_coef: Union[str, float] = "auto"
    target_update_interval: int = 1
    target_entropy: Union[str, float] = "auto"


@dataclass
class CriticWeightsConfig:
    """评论家权重配置类"""
    obstacle: float = 0.4
    guidance: float = 0.4
    environment: float = 0.2
    
    def __post_init__(self):
        """验证权重和为1"""
        total = self.obstacle + self.guidance + self.environment
        if abs(total - 1.0) > 1e-6:
            raise ValueError(f"评论家权重总和必须为1.0，当前为{total}")


@dataclass
class NetworkConfig:
    """网络配置类"""
    policy_kwargs: Dict[str, Any] = field(default_factory=lambda: {
        "net_arch": [256, 256],
        "activation_fn": "relu"
    })
    actor: Dict[str, Any] = field(default_factory=lambda: {
        "hidden_sizes": [256, 256],
        "activation": "relu",
        "output_activation": "tanh"
    })
    critic: Dict[str, Any] = field(default_factory=lambda: {
        "hidden_sizes": [256, 256],
        "activation": "relu",
        "n_critics": 2
    })


@dataclass
class EnvironmentConfig:
    """环境配置类"""
    name: str = "NavigationEnv"
    max_episode_steps: int = 1000
    state_space: Dict[str, int] = field(default_factory=lambda: {
        "radar_points": 36,
        "target_vector": 2,
        "self_state": 3,  # 修复：包含航向角，现在是3维
        "environment_data": 4
    })
    action_space: Dict[str, Any] = field(default_factory=lambda: {
        "type": "continuous",
        "low": [-1.0, -1.0],
        "high": [1.0, 1.0]
    })


@dataclass
class TrainingConfig:
    """训练配置类"""
    total_timesteps: int = 1000000
    eval_freq: int = 10000
    n_eval_episodes: int = 10
    eval_log_path: str = "./logs/eval/"
    n_envs: int = 4
    vec_env_type: str = "dummy"


@dataclass
class RewardsConfig:
    """奖励配置类"""
    obstacle: Dict[str, float] = field(default_factory=lambda: {
        "collision_penalty": -50.0,
        "safety_threshold": 2.0,
        "distance_scale": 10.0
    })
    guidance: Dict[str, float] = field(default_factory=lambda: {
        "goal_reward": 100.0,
        "progress_scale": 1.0,
        "time_penalty": -0.01
    })
    environment: Dict[str, float] = field(default_factory=lambda: {
        "current_scale": 0.5,
        "wind_scale": 0.3
    })


@dataclass
class LoggingConfig:
    """日志配置类"""
    tensorboard_log: str = "./logs/tensorboard/"
    log_interval: int = 100
    save_freq: int = 50000
    model_save_path: str = "./models/"
    metrics: list = field(default_factory=lambda: [
        "total_reward", "obstacle_reward", "guidance_reward",
        "environment_reward", "actor_loss", "critic_loss", "episode_length"
    ])


@dataclass
class DeviceConfig:
    """设备配置类"""
    use_cuda: bool = True
    cuda_device: int = 0


@dataclass
class Config:
    """主配置类"""
    algorithm: AlgorithmConfig = field(default_factory=AlgorithmConfig)
    critic_weights: CriticWeightsConfig = field(default_factory=CriticWeightsConfig)
    network: NetworkConfig = field(default_factory=NetworkConfig)
    environment: EnvironmentConfig = field(default_factory=EnvironmentConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    rewards: RewardsConfig = field(default_factory=RewardsConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    device: DeviceConfig = field(default_factory=DeviceConfig)
    seed: int = 42
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'Config':
        """从字典创建配置对象"""
        return cls(
            algorithm=AlgorithmConfig(**config_dict.get('algorithm', {})),
            critic_weights=CriticWeightsConfig(**config_dict.get('critic_weights', {})),
            network=NetworkConfig(**config_dict.get('network', {})),
            environment=EnvironmentConfig(**config_dict.get('environment', {})),
            training=TrainingConfig(**config_dict.get('training', {})),
            rewards=RewardsConfig(**config_dict.get('rewards', {})),
            logging=LoggingConfig(**config_dict.get('logging', {})),
            device=DeviceConfig(**config_dict.get('device', {})),
            seed=config_dict.get('seed', 42)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'algorithm': self.algorithm.__dict__,
            'critic_weights': self.critic_weights.__dict__,
            'network': self.network.__dict__,
            'environment': self.environment.__dict__,
            'training': self.training.__dict__,
            'rewards': self.rewards.__dict__,
            'logging': self.logging.__dict__,
            'device': self.device.__dict__,
            'seed': self.seed
        }


def load_config(config_path: Union[str, Path]) -> Config:
    """
    从YAML文件加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Config: 配置对象
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML格式错误
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        return Config.from_dict(config_dict)
    
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"配置文件格式错误: {e}")


def save_config(config: Config, config_path: Union[str, Path]) -> None:
    """
    保存配置到YAML文件
    
    Args:
        config: 配置对象
        config_path: 保存路径
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config.to_dict(), f, default_flow_style=False, 
                 allow_unicode=True, indent=2)


def get_default_config() -> Config:
    """获取默认配置"""
    return Config()


def validate_config(config: Config) -> bool:
    """
    验证配置的有效性
    
    Args:
        config: 配置对象
        
    Returns:
        bool: 配置是否有效
        
    Raises:
        ValueError: 配置无效时抛出异常
    """
    # 验证学习率
    if config.algorithm.learning_rate <= 0:
        raise ValueError("学习率必须大于0")
    
    # 验证批次大小
    if config.algorithm.batch_size <= 0:
        raise ValueError("批次大小必须大于0")
    
    # 验证评论家权重
    weights = config.critic_weights
    total_weight = weights.obstacle + weights.guidance + weights.environment
    if abs(total_weight - 1.0) > 1e-6:
        raise ValueError(f"评论家权重总和必须为1.0，当前为{total_weight}")
    
    # 验证环境配置
    if config.environment.max_episode_steps <= 0:
        raise ValueError("最大回合步数必须大于0")
    
    return True
