"""
融合模块

实现多个评论家专家的Q值融合，生成统一的Q值用于策略更新。
支持加权求和、动态权重调整等融合策略。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from multi_critic_sac.critics.obstacle_expert import ObstacleExpert
from multi_critic_sac.critics.guidance_expert import GuidanceExpert
from multi_critic_sac.critics.environment_expert import EnvironmentExpert


class FusionModule(nn.Module):
    """
    评论家融合模块
    
    将多个专家评论家的Q值进行融合，生成统一的Q值。
    支持多种融合策略：固定权重、自适应权重、注意力机制等。
    """
    
    def __init__(
        self,
        obstacle_expert: ObstacleExpert,
        guidance_expert: GuidanceExpert,
        environment_expert: EnvironmentExpert,
        fusion_weights: Dict[str, float] = None,
        fusion_strategy: str = "weighted_sum",
        adaptive_weights: bool = False,
        device: torch.device = torch.device("cpu")
    ):
        """
        初始化融合模块
        
        Args:
            obstacle_expert: 避障专家
            guidance_expert: 引导专家
            environment_expert: 环境影响专家
            fusion_weights: 融合权重字典
            fusion_strategy: 融合策略 ("weighted_sum", "attention", "dynamic")
            adaptive_weights: 是否使用自适应权重
            device: 计算设备
        """
        super(FusionModule, self).__init__()
        
        self.obstacle_expert = obstacle_expert
        self.guidance_expert = guidance_expert
        self.environment_expert = environment_expert
        self.fusion_strategy = fusion_strategy
        self.adaptive_weights = adaptive_weights
        self.device = device
        
        # 设置默认权重
        if fusion_weights is None:
            fusion_weights = {
                'obstacle': 0.4,
                'guidance': 0.4,
                'environment': 0.2
            }
        
        # 验证权重和为1
        total_weight = sum(fusion_weights.values())
        if abs(total_weight - 1.0) > 1e-6:
            raise ValueError(f"融合权重总和必须为1.0，当前为{total_weight}")
        
        self.fusion_weights = fusion_weights
        
        # 如果使用自适应权重，创建权重网络
        if adaptive_weights:
            self._init_adaptive_weights()
        
        # 如果使用注意力机制，创建注意力网络
        if fusion_strategy == "attention":
            self._init_attention_mechanism()
        
        print(f"融合模块初始化完成: 策略={fusion_strategy}, "
              f"权重={fusion_weights}, 自适应={adaptive_weights}")
    
    def _init_adaptive_weights(self) -> None:
        """初始化自适应权重网络"""
        state_dim = self.obstacle_expert.state_dim
        action_dim = self.obstacle_expert.action_dim
        input_dim = state_dim + action_dim
        
        self.weight_network = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3),  # 三个专家的权重
            nn.Softmax(dim=-1)  # 确保权重和为1
        ).to(self.device)
        
        print("自适应权重网络初始化完成")
    
    def _init_attention_mechanism(self) -> None:
        """初始化注意力机制"""
        state_dim = self.obstacle_expert.state_dim
        action_dim = self.obstacle_expert.action_dim
        input_dim = state_dim + action_dim
        
        # 查询、键、值网络
        self.query_network = nn.Linear(input_dim, 64).to(self.device)
        self.key_networks = nn.ModuleList([
            nn.Linear(1, 64).to(self.device) for _ in range(3)  # 三个专家
        ])
        self.value_networks = nn.ModuleList([
            nn.Linear(1, 64).to(self.device) for _ in range(3)  # 三个专家
        ])
        
        self.attention_output = nn.Linear(64, 1).to(self.device)
        
        print("注意力机制初始化完成")
    
    def get_expert_q_values(self, state: torch.Tensor, 
                          action: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取所有专家的Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Dict[str, torch.Tensor]: 各专家Q值字典
        """
        q_values = {
            'obstacle': self.obstacle_expert.get_q_values(state, action),
            'guidance': self.guidance_expert.get_q_values(state, action),
            'environment': self.environment_expert.get_q_values(state, action)
        }
        
        return q_values
    
    def weighted_sum_fusion(self, q_values: Dict[str, torch.Tensor],
                          weights: Optional[Dict[str, float]] = None) -> torch.Tensor:
        """
        加权求和融合
        
        Args:
            q_values: 各专家Q值
            weights: 融合权重（可选）
            
        Returns:
            torch.Tensor: 融合后的Q值
        """
        if weights is None:
            weights = self.fusion_weights
        
        fused_q = (weights['obstacle'] * q_values['obstacle'] +
                  weights['guidance'] * q_values['guidance'] +
                  weights['environment'] * q_values['environment'])
        
        return fused_q
    
    def adaptive_weighted_fusion(self, state: torch.Tensor, action: torch.Tensor,
                               q_values: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        自适应权重融合
        
        Args:
            state: 输入状态
            action: 输入动作
            q_values: 各专家Q值
            
        Returns:
            torch.Tensor: 融合后的Q值
        """
        # 拼接状态和动作
        input_tensor = torch.cat([state, action], dim=-1)
        
        # 计算自适应权重
        adaptive_weights = self.weight_network(input_tensor)
        
        # 加权融合
        fused_q = (adaptive_weights[:, 0:1] * q_values['obstacle'] +
                  adaptive_weights[:, 1:2] * q_values['guidance'] +
                  adaptive_weights[:, 2:3] * q_values['environment'])
        
        return fused_q
    
    def attention_fusion(self, state: torch.Tensor, action: torch.Tensor,
                        q_values: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        注意力机制融合
        
        Args:
            state: 输入状态
            action: 输入动作
            q_values: 各专家Q值
            
        Returns:
            torch.Tensor: 融合后的Q值
        """
        # 拼接状态和动作作为查询
        query_input = torch.cat([state, action], dim=-1)
        query = self.query_network(query_input)
        
        # 计算注意力权重
        q_list = [q_values['obstacle'], q_values['guidance'], q_values['environment']]
        attention_weights = []
        
        for i, q_val in enumerate(q_list):
            key = self.key_networks[i](q_val)
            attention_score = torch.sum(query * key, dim=-1, keepdim=True)
            attention_weights.append(attention_score)
        
        # 应用softmax归一化
        attention_weights = torch.cat(attention_weights, dim=-1)
        attention_weights = torch.softmax(attention_weights, dim=-1)
        
        # 计算加权值
        weighted_values = []
        for i, q_val in enumerate(q_list):
            value = self.value_networks[i](q_val)
            weighted_value = attention_weights[:, i:i+1] * value
            weighted_values.append(weighted_value)
        
        # 融合
        fused_features = sum(weighted_values)
        fused_q = self.attention_output(fused_features)
        
        return fused_q
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 融合后的Q值
        """
        # 获取各专家Q值
        q_values = self.get_expert_q_values(state, action)
        
        # 根据融合策略进行融合
        if self.fusion_strategy == "weighted_sum":
            if self.adaptive_weights:
                fused_q = self.adaptive_weighted_fusion(state, action, q_values)
            else:
                fused_q = self.weighted_sum_fusion(q_values)
        elif self.fusion_strategy == "attention":
            fused_q = self.attention_fusion(state, action, q_values)
        else:
            raise ValueError(f"不支持的融合策略: {self.fusion_strategy}")
        
        return fused_q
    
    def get_fusion_weights(self, state: torch.Tensor, 
                         action: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取当前的融合权重
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Dict[str, torch.Tensor]: 融合权重字典
        """
        if self.adaptive_weights and self.fusion_strategy == "weighted_sum":
            input_tensor = torch.cat([state, action], dim=-1)
            adaptive_weights = self.weight_network(input_tensor)
            
            return {
                'obstacle': adaptive_weights[:, 0],
                'guidance': adaptive_weights[:, 1],
                'environment': adaptive_weights[:, 2]
            }
        else:
            # 返回固定权重
            batch_size = state.shape[0]
            return {
                'obstacle': torch.full((batch_size,), self.fusion_weights['obstacle'], 
                                     device=self.device),
                'guidance': torch.full((batch_size,), self.fusion_weights['guidance'], 
                                     device=self.device),
                'environment': torch.full((batch_size,), self.fusion_weights['environment'], 
                                        device=self.device)
            }
    
    def analyze_fusion_weights(self, state: torch.Tensor, 
                             action: torch.Tensor) -> Dict[str, float]:
        """
        分析融合权重分布
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Dict[str, float]: 权重分析结果
        """
        weights = self.get_fusion_weights(state, action)
        
        analysis = {
            'mean_obstacle_weight': weights['obstacle'].mean().item(),
            'mean_guidance_weight': weights['guidance'].mean().item(),
            'mean_environment_weight': weights['environment'].mean().item(),
            'std_obstacle_weight': weights['obstacle'].std().item(),
            'std_guidance_weight': weights['guidance'].std().item(),
            'std_environment_weight': weights['environment'].std().item(),
        }
        
        return analysis
    
    def update_fusion_weights(self, new_weights: Dict[str, float]) -> None:
        """
        更新融合权重
        
        Args:
            new_weights: 新的权重字典
        """
        # 验证权重和为1
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 1e-6:
            raise ValueError(f"融合权重总和必须为1.0，当前为{total_weight}")
        
        self.fusion_weights = new_weights
        print(f"融合权重已更新: {new_weights}")
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'fusion_weights': self.fusion_weights,
            'fusion_strategy': self.fusion_strategy,
            'adaptive_weights': self.adaptive_weights
        }
        
        if self.adaptive_weights:
            checkpoint['weight_network_state_dict'] = self.weight_network.state_dict()
        
        if self.fusion_strategy == "attention":
            checkpoint['query_network_state_dict'] = self.query_network.state_dict()
            checkpoint['key_networks_state_dict'] = [net.state_dict() for net in self.key_networks]
            checkpoint['value_networks_state_dict'] = [net.state_dict() for net in self.value_networks]
            checkpoint['attention_output_state_dict'] = self.attention_output.state_dict()
        
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.fusion_weights = checkpoint['fusion_weights']
        self.fusion_strategy = checkpoint['fusion_strategy']
        self.adaptive_weights = checkpoint['adaptive_weights']
        
        if self.adaptive_weights and 'weight_network_state_dict' in checkpoint:
            self.weight_network.load_state_dict(checkpoint['weight_network_state_dict'])
        
        if (self.fusion_strategy == "attention" and 
            'query_network_state_dict' in checkpoint):
            self.query_network.load_state_dict(checkpoint['query_network_state_dict'])
            for i, state_dict in enumerate(checkpoint['key_networks_state_dict']):
                self.key_networks[i].load_state_dict(state_dict)
            for i, state_dict in enumerate(checkpoint['value_networks_state_dict']):
                self.value_networks[i].load_state_dict(state_dict)
            self.attention_output.load_state_dict(checkpoint['attention_output_state_dict'])
    
    def __repr__(self) -> str:
        """返回融合模块的字符串表示"""
        return (f"FusionModule(strategy={self.fusion_strategy}, "
                f"weights={self.fusion_weights}, "
                f"adaptive={self.adaptive_weights})")
