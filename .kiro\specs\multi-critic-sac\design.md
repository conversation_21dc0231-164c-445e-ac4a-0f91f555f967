# Design Document

## Overview

多评论家SAC（Multi-Critic SAC）系统是基于Stable-Baselines3 SAC算法的扩展，专门设计用于多目标强化学习和奖励分解。系统采用三个专门的评论家组来处理不同的目标：避障、路径引导和海洋环境影响。每个评论家组独立学习其专门领域的价值函数，然后通过融合机制生成统一的策略更新信号。

系统支持向量化环境训练，能够处理复杂的海洋环境状态表示，包括360度雷达数据、目标指向、智能体状态和海洋环境要素。

## Architecture

### 系统架构图

```mermaid
graph TB
    subgraph "Environment"
        E[海洋环境]
        E --> S[状态: 雷达+目标指向+智能体状态+海洋数据]
        S --> R1[避障奖励]
        S --> R2[引导奖励] 
        S --> R3[环境影响奖励]
    end
    
    subgraph "Multi-Critic SAC Agent"
        A[Actor Network 演员网络]
        
        subgraph "Critic Groups"
            C1[避障专家评论家组]
            C2[引导专家评论家组]
            C3[环境影响专家评论家组]
        end
        
        F[Fusion Module 融合模块]
        C1 --> F
        C2 --> F
        C3 --> F
        F --> A
    end
    
    subgraph "Training Infrastructure"
        VE[向量化环境]
        RB[经验回放缓冲区]
        TM[训练监控]
    end
    
    A --> E
    VE --> RB
    RB --> C1
    RB --> C2
    RB --> C3
    TM --> C1
    TM --> C2
    TM --> C3
```

### 核心组件

1. **Multi-Critic SAC Agent**: 主要的强化学习智能体
2. **三个专门评论家组**: 每个处理特定目标的价值估计
3. **融合模块**: 整合多个评论家的输出
4. **向量化环境**: 支持多线程并行训练
5. **状态处理模块**: 处理复杂的海洋环境状态

## Components and Interfaces

### 1. MultiCriticSAC 主类

```python
class MultiCriticSAC(SAC):
    """
    基于Stable-Baselines3 SAC的多评论家扩展
    继承SAC的核心功能，添加多评论家支持
    """
    
    def __init__(
        self,
        policy: str,
        env: Union[GymEnv, str],
        critic_groups: Dict[str, CriticGroup],
        fusion_method: str = "weighted_average",
        **kwargs
    ):
        pass
    
    def _setup_critics(self) -> None:
        """设置三个专门的评论家组"""
        pass
    
    def _update_critics(self, replay_data: ReplayBufferSamples) -> None:
        """更新所有评论家组"""
        pass
    
    def _fuse_critic_outputs(self, critic_outputs: Dict) -> torch.Tensor:
        """融合多个评论家的输出"""
        pass
```

### 2. CriticGroup 评论家组

```python
class CriticGroup:
    """
    单个评论家组的实现
    每个组专门处理特定类型的奖励和价值估计
    """
    
    def __init__(
        self,
        name: str,
        reward_type: str,
        network_arch: List[int],
        learning_rate: float
    ):
        pass
    
    def compute_target_q_value(
        self, 
        next_observations: torch.Tensor,
        rewards: torch.Tensor,
        dones: torch.Tensor
    ) -> torch.Tensor:
        """计算目标Q值"""
        pass
    
    def update(self, replay_data: ReplayBufferSamples) -> Dict[str, float]:
        """更新评论家网络"""
        pass
```

### 3. StateProcessor 状态处理器

```python
class StateProcessor:
    """
    处理复杂的海洋环境状态表示
    """
    
    def __init__(self):
        self.radar_directions = 36  # 36个方向，每10度一个
        
    def process_radar_data(
        self, 
        radar_raw: np.ndarray,
        agent_position: np.ndarray,
        agent_heading: float
    ) -> np.ndarray:
        """将雷达数据转换到局部坐标系"""
        pass
    
    def process_target_direction(
        self,
        agent_position: np.ndarray,
        target_position: np.ndarray,
        agent_heading: float
    ) -> np.ndarray:
        """计算目标指向的局部坐标表示"""
        pass
    
    def process_ocean_data(
        self,
        current_vector: np.ndarray,
        wave_height: float,
        wind_data: np.ndarray
    ) -> np.ndarray:
        """处理海洋环境数据"""
        pass
    
    def build_state_vector(
        self,
        radar_data: np.ndarray,
        target_direction: np.ndarray,
        agent_velocity: float,
        ocean_data: np.ndarray
    ) -> np.ndarray:
        """构建完整的状态向量"""
        pass
```

### 4. RewardDecomposer 奖励分解器

```python
class RewardDecomposer:
    """
    将环境奖励分解为三个专门的奖励信号
    """
    
    def decompose_reward(
        self,
        total_reward: float,
        state: np.ndarray,
        action: np.ndarray,
        next_state: np.ndarray,
        info: Dict
    ) -> Dict[str, float]:
        """
        分解奖励为三个组件：
        - obstacle_avoidance: 避障奖励
        - guidance: 引导奖励  
        - environment_impact: 环境影响奖励
        """
        pass
    
    def compute_obstacle_reward(self, radar_data: np.ndarray) -> float:
        """计算避障奖励"""
        pass
    
    def compute_guidance_reward(
        self, 
        agent_pos: np.ndarray,
        target_pos: np.ndarray,
        prev_distance: float
    ) -> float:
        """计算引导奖励"""
        pass
    
    def compute_environment_reward(self, ocean_data: np.ndarray) -> float:
        """计算环境影响奖励"""
        pass
```

### 5. VectorizedEnvironment 向量化环境

```python
class VectorizedOceanEnvironment:
    """
    支持多线程并行训练的向量化环境
    """
    
    def __init__(
        self,
        env_fns: List[Callable],
        n_envs: int,
        start_method: str = "spawn"
    ):
        pass
    
    def reset(self) -> np.ndarray:
        """重置所有环境"""
        pass
    
    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, List[Dict]]:
        """在所有环境中执行动作"""
        pass
    
    def close(self) -> None:
        """关闭所有环境"""
        pass
```

## Data Models

### 状态表示

```python
@dataclass
class OceanState:
    """海洋环境状态表示"""
    radar_data: np.ndarray          # 形状: (36,) - 36个方向的距离数据
    target_direction: np.ndarray    # 形状: (2,) - 局部坐标系下的目标方向
    agent_velocity: float           # 智能体速度大小
    ocean_current: np.ndarray       # 形状: (2,) - 洋流向量
    wave_height: float              # 海浪高度
    wind_vector: np.ndarray         # 形状: (2,) - 风向量
    
    def to_vector(self) -> np.ndarray:
        """转换为神经网络输入向量"""
        return np.concatenate([
            self.radar_data,                    # 36维
            self.target_direction,              # 2维
            [self.agent_velocity],              # 1维
            self.ocean_current,                 # 2维
            [self.wave_height],                 # 1维
            self.wind_vector                    # 2维
        ])  # 总计44维
```

### 动作表示

```python
@dataclass
class OceanAction:
    """海洋环境动作表示"""
    velocity_magnitude: float       # 速度大小 [-1, 1] 支持前进和后退
    heading_change: float          # 方向改变 [-1, 1] 对应 [-π, π]
    
    def to_vector(self) -> np.ndarray:
        """转换为环境执行向量"""
        return np.array([self.velocity_magnitude, self.heading_change])
    
    @classmethod
    def from_vector(cls, action_vector: np.ndarray) -> 'OceanAction':
        """从向量创建动作对象"""
        return cls(
            velocity_magnitude=action_vector[0],
            heading_change=action_vector[1]
        )
```

### 奖励分解

```python
@dataclass
class DecomposedReward:
    """分解后的奖励信号"""
    obstacle_avoidance: float       # 避障奖励
    guidance: float                 # 引导奖励
    environment_impact: float       # 环境影响奖励
    total: float                   # 总奖励
    
    def get_reward_for_critic(self, critic_type: str) -> float:
        """获取特定评论家的奖励"""
        reward_map = {
            "obstacle_avoidance": self.obstacle_avoidance,
            "guidance": self.guidance,
            "environment_impact": self.environment_impact
        }
        return reward_map.get(critic_type, 0.0)
```

## Error Handling

### 1. 训练错误处理

```python
class MultiCriticTrainingError(Exception):
    """多评论家训练相关错误"""
    pass

class CriticConvergenceError(MultiCriticTrainingError):
    """评论家收敛错误"""
    pass

class RewardDecompositionError(MultiCriticTrainingError):
    """奖励分解错误"""
    pass
```

### 2. 错误恢复策略

- **评论家训练失败**: 降低学习率，重新初始化网络权重
- **奖励分解异常**: 使用默认奖励分配策略
- **向量化环境错误**: 自动重启失败的环境实例
- **状态处理错误**: 使用上一个有效状态或默认状态

### 3. 监控和日志

```python
class MultiCriticLogger:
    """多评论家训练监控"""
    
    def log_critic_performance(
        self,
        critic_name: str,
        loss: float,
        q_value_mean: float,
        reward_contribution: float
    ) -> None:
        """记录评论家性能指标"""
        pass
    
    def log_fusion_weights(self, weights: Dict[str, float]) -> None:
        """记录融合权重"""
        pass
    
    def detect_training_anomalies(self) -> List[str]:
        """检测训练异常"""
        pass
```

## Testing Strategy

### 1. 单元测试

- **StateProcessor测试**: 验证状态转换的正确性
- **RewardDecomposer测试**: 验证奖励分解逻辑
- **CriticGroup测试**: 验证单个评论家的训练
- **融合模块测试**: 验证多评论家输出融合

### 2. 性能测试

- **训练速度基准**: 与标准SAC对比训练速度
- **收敛性测试**: 验证多评论家的收敛特性
- **内存使用测试**: 监控多评论家的内存开销

### 3. 海洋环境特定测试

```python
class OceanEnvironmentTests:
    """海洋环境特定测试"""
    
    def test_radar_coordinate_transformation(self):
        """测试雷达坐标转换"""
        pass
    
    def test_ocean_current_impact(self):
        """测试洋流影响处理"""
        pass
    
    def test_obstacle_avoidance_behavior(self):
        """测试避障行为"""
        pass
    
    def test_target_reaching_behavior(self):
        """测试目标到达行为"""
        pass
```

### 4. 奖励缩放机制

```python
class RewardScaler:
    """奖励缩放器，平衡多个奖励和惩罚的大小"""
    
    def __init__(
        self,
        obstacle_scale: float = 1.0,
        guidance_scale: float = 1.0,
        environment_scale: float = 1.0,
        max_reward_magnitude: float = 10.0
    ):
        self.scales = {
            "obstacle_avoidance": obstacle_scale,
            "guidance": guidance_scale,
            "environment_impact": environment_scale
        }
        self.max_magnitude = max_reward_magnitude
    
    def scale_reward(self, reward_type: str, raw_reward: float) -> float:
        """缩放单个奖励"""
        scaled = raw_reward * self.scales.get(reward_type, 1.0)
        # 限制奖励幅度
        return np.clip(scaled, -self.max_magnitude, self.max_magnitude)
    
    def scale_decomposed_reward(self, decomposed_reward: DecomposedReward) -> DecomposedReward:
        """缩放分解后的奖励"""
        return DecomposedReward(
            obstacle_avoidance=self.scale_reward("obstacle_avoidance", decomposed_reward.obstacle_avoidance),
            guidance=self.scale_reward("guidance", decomposed_reward.guidance),
            environment_impact=self.scale_reward("environment_impact", decomposed_reward.environment_impact),
            total=decomposed_reward.total
        )
```

这个设计提供了一个完整的多评论家SAC框架，支持海洋环境的复杂状态表示、多目标优化、向量化训练和奖励缩放平衡功能。