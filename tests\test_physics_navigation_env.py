"""
测试基于Pymunk物理引擎的增强版海洋导航环境

验证物理环境的功能完整性、兼容性和性能特性。
"""

import sys
import os
import warnings

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import time
from typing import Dict, Any

# 尝试导入物理环境
try:
    from multi_critic_sac.envs.physics_navigation_env import PhysicsNavigationEnv
    PHYSICS_ENV_AVAILABLE = True
except ImportError as e:
    PHYSICS_ENV_AVAILABLE = False
    print(f"物理环境不可用: {e}")

from multi_critic_sac.envs.navigation_env import NavigationEnv


def test_physics_environment_availability():
    """测试物理环境的可用性"""
    print("🔧 测试1: 物理环境可用性检查")
    print("=" * 50)
    
    if PHYSICS_ENV_AVAILABLE:
        print("✅ PhysicsNavigationEnv 可用")
        
        # 测试创建物理环境
        try:
            env = PhysicsNavigationEnv(use_physics_engine=True, seed=42)
            print("✅ 物理环境创建成功")
            
            # 检查物理引擎状态
            if env.use_physics_engine:
                print("✅ Pymunk物理引擎已启用")
            else:
                print("⚠️  物理引擎未启用，已降级为原始环境")
            
            env.close() if hasattr(env, 'close') else None
            return True
            
        except Exception as e:
            print(f"❌ 物理环境创建失败: {e}")
            return False
    else:
        print("❌ PhysicsNavigationEnv 不可用")
        return False


def test_interface_compatibility():
    """测试接口兼容性"""
    print("\n🔄 测试2: 接口兼容性验证")
    print("=" * 50)
    
    if not PHYSICS_ENV_AVAILABLE:
        print("⏭️  跳过测试（物理环境不可用）")
        return True
    
    try:
        # 创建原始环境和物理环境
        original_env = NavigationEnv(seed=42)
        physics_env = PhysicsNavigationEnv(use_physics_engine=True, seed=42)
        
        print("📊 比较环境接口:")
        
        # 检查观测空间
        orig_obs, _ = original_env.reset()
        phys_obs, _ = physics_env.reset()
        
        print(f"  原始环境观测维度: {orig_obs.shape}")
        print(f"  物理环境观测维度: {phys_obs.shape}")
        assert orig_obs.shape == phys_obs.shape, "观测维度不匹配"
        print("✅ 观测空间兼容")
        
        # 检查动作空间
        print(f"  原始环境动作空间: {original_env.action_space}")
        print(f"  物理环境动作空间: {physics_env.action_space}")
        assert str(original_env.action_space) == str(physics_env.action_space), "动作空间不匹配"
        print("✅ 动作空间兼容")
        
        # 检查step方法返回格式
        action = physics_env.action_space.sample()
        
        orig_result = original_env.step(action)
        phys_result = physics_env.step(action)
        
        assert len(orig_result) == len(phys_result), "step返回值数量不匹配"
        assert orig_result[0].shape == phys_result[0].shape, "观测形状不匹配"
        print("✅ step方法兼容")
        
        # 检查info字典结构
        orig_info = orig_result[4]
        phys_info = phys_result[4]
        
        # 基础info字段应该存在
        basic_fields = ['obstacle_reward', 'guidance_reward', 'environment_reward']
        for field in basic_fields:
            assert field in orig_info, f"原始环境缺少{field}"
            assert field in phys_info, f"物理环境缺少{field}"
        
        print("✅ info字典结构兼容")
        
        original_env.close() if hasattr(original_env, 'close') else None
        physics_env.close() if hasattr(physics_env, 'close') else None
        
        return True
        
    except Exception as e:
        print(f"❌ 接口兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_physics_features():
    """测试物理特性"""
    print("\n⚡ 测试3: 物理特性验证")
    print("=" * 50)
    
    if not PHYSICS_ENV_AVAILABLE:
        print("⏭️  跳过测试（物理环境不可用）")
        return True
    
    try:
        env = PhysicsNavigationEnv(
            use_physics_engine=True,
            enable_spatial_variation=True,
            enable_temporal_variation=True,
            seed=42
        )
        
        obs, info = env.reset()
        
        print("📊 物理环境特性测试:")
        
        # 测试物理信息获取
        physics_info = env.get_physics_info()
        if physics_info:
            print(f"  物理引擎状态: {physics_info['physics_enabled']}")
            print(f"  智能体位置: [{physics_info['agent_position'][0]:.2f}, {physics_info['agent_position'][1]:.2f}]")
            print(f"  智能体速度: {physics_info['agent_speed']:.2f}")
            print("✅ 物理信息获取正常")
        else:
            print("⚠️  物理信息为空")
        
        # 测试环境复杂度指标
        complexity_metrics = env.get_environment_complexity_metrics()
        print(f"  洋流强度: {complexity_metrics['current_strength']:.3f}")
        print(f"  风力强度: {complexity_metrics['wind_strength']:.3f}")
        print(f"  总环境力: {complexity_metrics['total_env_force']:.3f}")
        print("✅ 环境复杂度指标正常")
        
        # 测试多步仿真
        initial_pos = env.agent_pos.copy()
        
        for step in range(10):
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            
            if terminated or truncated:
                break
        
        final_pos = env.agent_pos.copy()
        distance_moved = np.linalg.norm(final_pos - initial_pos)
        
        print(f"  10步后移动距离: {distance_moved:.2f}")
        print("✅ 多步仿真正常")
        
        # 测试环境参数变化
        initial_current = env.current_vector.copy()
        initial_wind = env.wind_vector.copy()
        
        # 执行更多步骤观察环境变化
        for _ in range(50):
            action = env.action_space.sample()
            env.step(action)
        
        final_current = env.current_vector.copy()
        final_wind = env.wind_vector.copy()
        
        current_change = np.linalg.norm(final_current - initial_current)
        wind_change = np.linalg.norm(final_wind - initial_wind)
        
        print(f"  50步后洋流变化: {current_change:.3f}")
        print(f"  50步后风力变化: {wind_change:.3f}")
        
        if current_change > 0.01 or wind_change > 0.01:
            print("✅ 环境参数动态变化正常")
        else:
            print("⚠️  环境参数变化较小")
        
        env.close() if hasattr(env, 'close') else None
        
        return True
        
    except Exception as e:
        print(f"❌ 物理特性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_comparison():
    """测试性能对比"""
    print("\n🏃 测试4: 性能对比")
    print("=" * 50)
    
    if not PHYSICS_ENV_AVAILABLE:
        print("⏭️  跳过测试（物理环境不可用）")
        return True
    
    try:
        # 创建两种环境
        original_env = NavigationEnv(seed=42)
        physics_env = PhysicsNavigationEnv(use_physics_engine=True, seed=42)
        
        num_steps = 100
        
        # 测试原始环境性能
        start_time = time.time()
        obs, _ = original_env.reset()
        for _ in range(num_steps):
            action = original_env.action_space.sample()
            obs, reward, terminated, truncated, info = original_env.step(action)
            if terminated or truncated:
                obs, _ = original_env.reset()
        original_time = time.time() - start_time
        
        # 测试物理环境性能
        start_time = time.time()
        obs, _ = physics_env.reset()
        for _ in range(num_steps):
            action = physics_env.action_space.sample()
            obs, reward, terminated, truncated, info = physics_env.step(action)
            if terminated or truncated:
                obs, _ = physics_env.reset()
        physics_time = time.time() - start_time
        
        print(f"📊 性能对比 ({num_steps}步):")
        print(f"  原始环境: {original_time:.3f}秒 ({num_steps/original_time:.1f} FPS)")
        print(f"  物理环境: {physics_time:.3f}秒 ({num_steps/physics_time:.1f} FPS)")
        print(f"  性能比率: {physics_time/original_time:.2f}x")
        
        if physics_time / original_time < 5.0:
            print("✅ 物理环境性能可接受")
        else:
            print("⚠️  物理环境性能较慢")
        
        original_env.close() if hasattr(original_env, 'close') else None
        physics_env.close() if hasattr(physics_env, 'close') else None
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_graceful_degradation():
    """测试优雅降级"""
    print("\n🛡️  测试5: 优雅降级验证")
    print("=" * 50)
    
    if not PHYSICS_ENV_AVAILABLE:
        print("⏭️  跳过测试（物理环境不可用）")
        return True
    
    try:
        # 测试禁用物理引擎的情况
        env = PhysicsNavigationEnv(use_physics_engine=False, seed=42)
        
        print("📊 降级模式测试:")
        print(f"  物理引擎状态: {'启用' if env.use_physics_engine else '禁用'}")
        
        # 测试基本功能
        obs, info = env.reset()
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        print("✅ 降级模式基本功能正常")
        
        # 检查物理信息
        physics_info = env.get_physics_info()
        if not physics_info:
            print("✅ 降级模式下物理信息为空（符合预期）")
        
        env.close() if hasattr(env, 'close') else None
        
        return True
        
    except Exception as e:
        print(f"❌ 优雅降级测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始物理导航环境测试套件")
    print("=" * 60)
    
    tests = [
        test_physics_environment_availability,
        test_interface_compatibility,
        test_physics_features,
        test_performance_comparison,
        test_graceful_degradation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    print("\n📋 测试结果总结:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({test.__name__}): {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！物理导航环境已准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
