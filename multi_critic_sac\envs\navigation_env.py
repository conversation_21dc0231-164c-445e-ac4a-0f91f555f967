"""
导航环境模块

实现具体的导航环境，包括障碍物、目标点、环境因素等。
提供完整的44维状态空间和奖励计算。

作者: Multi-Critic SAC Team
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
from multi_critic_sac.envs.base_env import BaseEnv
from multi_critic_sac.utils.common import normalize_angle, calculate_distance


class NavigationEnv(BaseEnv):
    """
    导航环境
    
    智能体需要在包含障碍物的环境中导航到目标点，
    同时考虑洋流、风力等环境因素的影响。
    """
    
    def __init__(
        self,
        map_size: float = 100.0,
        num_obstacles: int = 10,
        obstacle_radius_range: Tuple[float, float] = (2.0, 5.0),
        goal_threshold: float = 1.0,
        max_episode_steps: int = 1000,
        render_mode: Optional[str] = None,
        seed: Optional[int] = None
    ):
        """
        初始化导航环境
        
        Args:
            map_size: 地图大小
            num_obstacles: 障碍物数量
            obstacle_radius_range: 障碍物半径范围
            goal_threshold: 目标到达阈值
            max_episode_steps: 最大回合步数
            render_mode: 渲染模式
            seed: 随机种子
        """
        self.map_size = map_size
        self.num_obstacles = num_obstacles
        self.obstacle_radius_range = obstacle_radius_range
        self.goal_threshold = goal_threshold
        
        # 智能体状态
        self.agent_pos = np.array([0.0, 0.0])
        self.agent_heading = 0.0  # 朝向角度（弧度）
        self.agent_velocity = 0.0
        self.agent_angular_velocity = 0.0
        
        # 目标位置
        self.goal_pos = np.array([0.0, 0.0])
        
        # 障碍物列表 [(x, y, radius), ...]
        self.obstacles = []
        
        # 环境因素
        self.current_vector = np.array([0.0, 0.0])  # 洋流向量
        self.wind_vector = np.array([0.0, 0.0])     # 风力向量
        
        # 雷达参数
        self.radar_range = 20.0  # 雷达探测范围
        self.radar_angles = np.linspace(0, 2*np.pi, 36, endpoint=False)  # 36个方向
        
        # 动作缩放参数
        self.max_linear_velocity = 5.0
        self.max_angular_velocity = np.pi
        
        # 初始化基类
        super().__init__(max_episode_steps, render_mode, seed)
        
        print(f"导航环境初始化完成: 地图大小={map_size}, 障碍物数量={num_obstacles}")
    
    def _reset_environment(self) -> None:
        """重置环境状态"""
        # 生成障碍物
        self._generate_obstacles()
        
        # 随机放置智能体和目标
        self._place_agent_and_goal()
        
        # 生成环境因素
        self._generate_environment_factors()
        
        # 重置智能体状态
        self.agent_heading = np.random.uniform(0, 2*np.pi)
        self.agent_velocity = 0.0
        self.agent_angular_velocity = 0.0
    
    def _generate_obstacles(self) -> None:
        """生成随机障碍物"""
        self.obstacles = []
        
        for _ in range(self.num_obstacles):
            # 随机位置
            x = np.random.uniform(-self.map_size/2, self.map_size/2)
            y = np.random.uniform(-self.map_size/2, self.map_size/2)
            
            # 随机半径
            radius = np.random.uniform(*self.obstacle_radius_range)
            
            self.obstacles.append((x, y, radius))
    
    def _place_agent_and_goal(self) -> None:
        """放置智能体和目标点"""
        max_attempts = 100
        
        # 放置智能体
        for _ in range(max_attempts):
            pos = np.random.uniform(-self.map_size/2 + 10, self.map_size/2 - 10, 2)
            if not self._is_position_in_obstacle(pos):
                self.agent_pos = pos
                break
        
        # 放置目标点（确保与智能体有一定距离）
        for _ in range(max_attempts):
            pos = np.random.uniform(-self.map_size/2 + 10, self.map_size/2 - 10, 2)
            if (not self._is_position_in_obstacle(pos) and 
                calculate_distance(pos, self.agent_pos) > 20.0):
                self.goal_pos = pos
                break
    
    def _generate_environment_factors(self) -> None:
        """生成环境因素"""
        # 洋流：随机方向和强度
        current_angle = np.random.uniform(0, 2*np.pi)
        current_strength = np.random.uniform(0.5, 2.0)
        self.current_vector = current_strength * np.array([
            np.cos(current_angle), np.sin(current_angle)
        ])
        
        # 风力：随机方向和强度
        wind_angle = np.random.uniform(0, 2*np.pi)
        wind_strength = np.random.uniform(0.3, 1.5)
        self.wind_vector = wind_strength * np.array([
            np.cos(wind_angle), np.sin(wind_angle)
        ])
    
    def _is_position_in_obstacle(self, pos: np.ndarray) -> bool:
        """检查位置是否在障碍物内"""
        for obs_x, obs_y, obs_radius in self.obstacles:
            if calculate_distance(pos, np.array([obs_x, obs_y])) < obs_radius:
                return True
        return False
    
    def _get_radar_data(self) -> np.ndarray:
        """获取雷达数据"""
        radar_data = np.full(36, self.radar_range)  # 初始化为最大探测距离
        
        for i, angle in enumerate(self.radar_angles):
            # 计算雷达射线方向（相对于智能体朝向）
            ray_angle = self.agent_heading + angle
            ray_direction = np.array([np.cos(ray_angle), np.sin(ray_angle)])
            
            # 检测与障碍物的交点
            min_distance = self.radar_range
            
            for obs_x, obs_y, obs_radius in self.obstacles:
                obs_pos = np.array([obs_x, obs_y])
                
                # 计算射线与圆形障碍物的交点
                distance = self._ray_circle_intersection(
                    self.agent_pos, ray_direction, obs_pos, obs_radius
                )
                
                if distance is not None and distance < min_distance:
                    min_distance = distance
            
            # 检测地图边界
            boundary_distance = self._ray_boundary_intersection(
                self.agent_pos, ray_direction
            )
            if boundary_distance < min_distance:
                min_distance = boundary_distance
            
            radar_data[i] = min_distance
        
        return radar_data
    
    def _ray_circle_intersection(self, ray_start: np.ndarray, ray_dir: np.ndarray,
                               circle_center: np.ndarray, circle_radius: float) -> Optional[float]:
        """计算射线与圆的交点距离"""
        # 向量从射线起点到圆心
        to_center = circle_center - ray_start
        
        # 投影长度
        proj_length = np.dot(to_center, ray_dir)
        
        # 如果投影为负，圆在射线后方
        if proj_length < 0:
            return None
        
        # 计算最近点到圆心的距离
        closest_point = ray_start + proj_length * ray_dir
        distance_to_center = calculate_distance(closest_point, circle_center)
        
        # 如果距离大于半径，没有交点
        if distance_to_center > circle_radius:
            return None
        
        # 计算交点距离
        chord_half_length = np.sqrt(circle_radius**2 - distance_to_center**2)
        intersection_distance = proj_length - chord_half_length
        
        return max(0, intersection_distance)
    
    def _ray_boundary_intersection(self, ray_start: np.ndarray, 
                                 ray_dir: np.ndarray) -> float:
        """计算射线与地图边界的交点距离"""
        # 地图边界
        boundaries = [
            (-self.map_size/2, self.map_size/2),  # x边界
            (-self.map_size/2, self.map_size/2)   # y边界
        ]
        
        min_distance = self.radar_range
        
        # 检查x边界
        if ray_dir[0] != 0:
            for x_bound in boundaries[0]:
                t = (x_bound - ray_start[0]) / ray_dir[0]
                if t > 0:
                    y_intersect = ray_start[1] + t * ray_dir[1]
                    if boundaries[1][0] <= y_intersect <= boundaries[1][1]:
                        min_distance = min(min_distance, t)
        
        # 检查y边界
        if ray_dir[1] != 0:
            for y_bound in boundaries[1]:
                t = (y_bound - ray_start[1]) / ray_dir[1]
                if t > 0:
                    x_intersect = ray_start[0] + t * ray_dir[0]
                    if boundaries[0][0] <= x_intersect <= boundaries[0][1]:
                        min_distance = min(min_distance, t)
        
        return min_distance
    
    def _get_observation(self) -> np.ndarray:
        """获取44维观测向量"""
        # 雷达数据 (36维)
        radar_data = self._get_radar_data()
        
        # 目标指向向量 (2维) - 在智能体局部坐标系中
        to_goal = self.goal_pos - self.agent_pos
        goal_distance = np.linalg.norm(to_goal)
        goal_angle = np.arctan2(to_goal[1], to_goal[0]) - self.agent_heading
        goal_angle = normalize_angle(goal_angle)
        target_vector = np.array([goal_distance, goal_angle])
        
        # 自身状态 (3维) - 添加航向角
        self_state = np.array([self.agent_velocity, self.agent_angular_velocity, self.agent_heading])

        # 环境数据 (4维)
        env_data = np.concatenate([self.current_vector, self.wind_vector])

        # 拼接所有观测
        observation = np.concatenate([
            radar_data,      # 36维
            target_vector,   # 2维
            self_state,      # 3维 (新增航向角)
            env_data         # 4维
        ])
        
        return observation.astype(np.float32)
    
    def _execute_action(self, action: np.ndarray) -> None:
        """执行动作"""
        # 解析动作
        linear_velocity_cmd = action[0] * self.max_linear_velocity
        angular_velocity_cmd = action[1] * self.max_angular_velocity
        
        # 更新智能体状态
        dt = 0.1  # 时间步长
        
        # 考虑环境因素的影响
        effective_velocity = linear_velocity_cmd
        
        # 洋流影响（简化模型）
        current_effect = np.dot(self.current_vector, 
                               np.array([np.cos(self.agent_heading), 
                                       np.sin(self.agent_heading)]))
        effective_velocity += current_effect
        
        # 更新位置和朝向
        self.agent_velocity = effective_velocity
        self.agent_angular_velocity = angular_velocity_cmd
        
        # 更新位置
        self.agent_pos[0] += effective_velocity * np.cos(self.agent_heading) * dt
        self.agent_pos[1] += effective_velocity * np.sin(self.agent_heading) * dt
        
        # 更新朝向
        self.agent_heading += angular_velocity_cmd * dt
        self.agent_heading = normalize_angle(self.agent_heading)
        
        # 限制在地图范围内
        self.agent_pos = np.clip(self.agent_pos,
                                -self.map_size/2 + 1,
                                self.map_size/2 - 1)

    def _calculate_reward(self, action: np.ndarray) -> Dict[str, float]:
        """计算奖励（包括分解奖励）"""
        rewards = {}

        # 避障奖励
        obstacle_reward = self._calculate_obstacle_reward()
        rewards['obstacle_reward'] = obstacle_reward

        # 引导奖励
        guidance_reward = self._calculate_guidance_reward()
        rewards['guidance_reward'] = guidance_reward

        # 环境奖励
        environment_reward = self._calculate_environment_reward(action)
        rewards['environment_reward'] = environment_reward

        # 总奖励
        total_reward = obstacle_reward + guidance_reward + environment_reward
        rewards['total_reward'] = total_reward

        return rewards

    def _calculate_obstacle_reward(self) -> float:
        """计算避障奖励"""
        # 获取最近障碍物距离
        min_distance = float('inf')

        for obs_x, obs_y, obs_radius in self.obstacles:
            obs_pos = np.array([obs_x, obs_y])
            distance = calculate_distance(self.agent_pos, obs_pos) - obs_radius
            min_distance = min(min_distance, distance)

        # 碰撞检测
        if min_distance < 0.5:
            return -50.0  # 碰撞惩罚

        # 安全距离奖励/惩罚
        safety_threshold = 2.0
        if min_distance < safety_threshold:
            return -10.0 * (safety_threshold - min_distance) / safety_threshold

        return 0.0  # 安全距离内无奖励

    def _calculate_guidance_reward(self) -> float:
        """计算引导奖励"""
        # 计算到目标的距离
        distance_to_goal = calculate_distance(self.agent_pos, self.goal_pos)

        # 检查是否到达目标
        if distance_to_goal < self.goal_threshold:
            return 100.0  # 到达目标奖励

        # 进度奖励（基于距离变化）
        # 这里简化为基于当前距离的负奖励，鼓励接近目标
        progress_reward = -0.1 * distance_to_goal / self.map_size

        # 时间惩罚
        time_penalty = -0.01

        return progress_reward + time_penalty

    def _calculate_environment_reward(self, action: np.ndarray) -> float:
        """计算环境影响奖励"""
        # 洋流影响
        agent_direction = np.array([np.cos(self.agent_heading), np.sin(self.agent_heading)])
        current_alignment = np.dot(self.current_vector, agent_direction)
        current_reward = 0.5 * current_alignment

        # 风力影响
        wind_alignment = np.dot(self.wind_vector, agent_direction)
        wind_reward = 0.3 * wind_alignment

        # 能量消耗惩罚
        energy_penalty = 0.1 * (action[0]**2 + 0.5 * action[1]**2)

        return current_reward + wind_reward - energy_penalty

    def _is_terminated(self) -> bool:
        """检查是否终止"""
        # 到达目标
        if calculate_distance(self.agent_pos, self.goal_pos) < self.goal_threshold:
            return True

        # 碰撞检测
        if self._is_position_in_obstacle(self.agent_pos):
            return True

        return False

    def _is_truncated(self) -> bool:
        """检查是否截断"""
        # 超出地图边界
        if (abs(self.agent_pos[0]) > self.map_size/2 - 1 or
            abs(self.agent_pos[1]) > self.map_size/2 - 1):
            return True

        return False

    def _render_human(self) -> None:
        """人类可视化渲染"""
        plt.figure(figsize=(10, 10))
        plt.clf()

        # 绘制地图边界
        boundary = self.map_size / 2
        plt.xlim(-boundary, boundary)
        plt.ylim(-boundary, boundary)

        # 绘制障碍物
        for obs_x, obs_y, obs_radius in self.obstacles:
            circle = plt.Circle((obs_x, obs_y), obs_radius, color='red', alpha=0.7)
            plt.gca().add_patch(circle)

        # 绘制智能体
        agent_circle = plt.Circle(self.agent_pos, 1.0, color='blue', alpha=0.8)
        plt.gca().add_patch(agent_circle)

        # 绘制智能体朝向
        heading_end = self.agent_pos + 2.0 * np.array([
            np.cos(self.agent_heading), np.sin(self.agent_heading)
        ])
        plt.arrow(self.agent_pos[0], self.agent_pos[1],
                 heading_end[0] - self.agent_pos[0],
                 heading_end[1] - self.agent_pos[1],
                 head_width=0.5, head_length=0.5, fc='blue', ec='blue')

        # 绘制目标点
        goal_circle = plt.Circle(self.goal_pos, self.goal_threshold,
                               color='green', alpha=0.7)
        plt.gca().add_patch(goal_circle)

        # 绘制环境因素（箭头）
        if np.linalg.norm(self.current_vector) > 0.1:
            plt.arrow(self.agent_pos[0] - 5, self.agent_pos[1] - 5,
                     self.current_vector[0], self.current_vector[1],
                     head_width=0.3, head_length=0.3, fc='cyan', ec='cyan',
                     label='洋流')

        if np.linalg.norm(self.wind_vector) > 0.1:
            plt.arrow(self.agent_pos[0] + 5, self.agent_pos[1] - 5,
                     self.wind_vector[0], self.wind_vector[1],
                     head_width=0.3, head_length=0.3, fc='orange', ec='orange',
                     label='风力')

        plt.title(f'导航环境 - 步数: {self.current_step}')
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.axis('equal')
        plt.pause(0.01)

    def _render_rgb_array(self) -> np.ndarray:
        """RGB数组渲染"""
        # 简化实现，返回空白图像
        # 实际项目中可以使用更复杂的渲染逻辑
        return np.zeros((400, 400, 3), dtype=np.uint8)

    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            'map_size': self.map_size,
            'num_obstacles': len(self.obstacles),
            'agent_position': self.agent_pos.copy(),
            'goal_position': self.goal_pos.copy(),
            'distance_to_goal': calculate_distance(self.agent_pos, self.goal_pos),
            'current_vector': self.current_vector.copy(),
            'wind_vector': self.wind_vector.copy(),
            'agent_heading': self.agent_heading,
            'min_obstacle_distance': min([
                calculate_distance(self.agent_pos, np.array([x, y])) - r
                for x, y, r in self.obstacles
            ]) if self.obstacles else float('inf')
        }
