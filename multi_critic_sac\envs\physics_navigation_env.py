"""
基于Pymunk物理引擎的增强版海洋导航环境

继承NavigationEnv，通过引入真实物理仿真提高环境的真实性和挑战性。
保持与现有Multi-Critic SAC训练流程完全兼容。

主要特性：
- 基于力的控制系统
- 真实的碰撞检测和响应
- 考虑惯性和动量的运动模型
- 时空相关的环境参数变化
- 多尺度环境变化（短期波动、中期趋势、长期模式）
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
import warnings

# 尝试导入Pymunk，如果不可用则优雅降级
try:
    import pymunk
    import pymunk.pygame_util
    PYMUNK_AVAILABLE = True
except ImportError:
    PYMUNK_AVAILABLE = False
    warnings.warn("Pymunk不可用，将使用原始导航环境。请安装pymunk: pip install pymunk")

from .navigation_env import NavigationEnv
from ..utils.common import calculate_distance, normalize_angle


class PhysicsNavigationEnv(NavigationEnv):
    """
    基于Pymunk物理引擎的增强版海洋导航环境
    
    在保持接口兼容性的前提下，引入真实物理仿真：
    - 基于力的控制系统
    - 真实碰撞检测
    - 惯性和动量建模
    - 时空相关环境变化
    """
    
    def __init__(
        self,
        # 原有参数（保持兼容性）
        map_size: float = 100.0,
        num_obstacles: int = 10,
        obstacle_radius_range: Tuple[float, float] = (2.0, 5.0),
        goal_threshold: float = 1.0,
        max_episode_steps: int = 1000,
        render_mode: Optional[str] = None,
        seed: Optional[int] = None,
        
        # 新增物理引擎参数
        use_physics_engine: bool = True,
        physics_dt: float = 0.01,
        physics_steps_per_env_step: int = 10,
        max_thrust_force: float = 1000.0,
        max_steering_torque: float = 500.0,
        drag_coefficient: float = 0.1,
        current_force_coefficient: float = 50.0,
        wind_force_coefficient: float = 30.0,
        agent_mass: float = 100.0,
        agent_radius: float = 1.0,
        enable_spatial_variation: bool = True,
        enable_temporal_variation: bool = True
    ):
        """
        初始化物理导航环境
        
        Args:
            use_physics_engine: 是否使用物理引擎（False时降级为原环境）
            physics_dt: 物理时间步长（秒）
            physics_steps_per_env_step: 每个环境步长的物理步数
            max_thrust_force: 最大推进力（牛顿）
            max_steering_torque: 最大转向力矩（牛顿·米）
            drag_coefficient: 阻力系数
            current_force_coefficient: 洋流力系数
            wind_force_coefficient: 风力系数
            agent_mass: 智能体质量（千克）
            agent_radius: 智能体半径（米）
            enable_spatial_variation: 启用基于位置的环境变化
            enable_temporal_variation: 启用基于时间的环境变化
        """
        # 检查物理引擎可用性
        self.use_physics_engine = use_physics_engine and PYMUNK_AVAILABLE
        if use_physics_engine and not PYMUNK_AVAILABLE:
            warnings.warn("Pymunk不可用，降级为原始导航环境")
            self.use_physics_engine = False
        
        # 物理引擎参数
        self.physics_dt = physics_dt
        self.physics_steps_per_env_step = physics_steps_per_env_step
        self.max_thrust_force = max_thrust_force
        self.max_steering_torque = max_steering_torque
        self.drag_coefficient = drag_coefficient
        self.current_force_coefficient = current_force_coefficient
        self.wind_force_coefficient = wind_force_coefficient
        self.agent_mass = agent_mass
        self.agent_radius = agent_radius
        
        # 环境变化参数
        self.enable_spatial_variation = enable_spatial_variation
        self.enable_temporal_variation = enable_temporal_variation
        
        # 物理世界组件
        self.space = None
        self.agent_body = None
        self.agent_shape = None
        self.obstacle_bodies = []
        self.wall_bodies = []
        
        # 环境变化状态
        self.physics_step_count = 0
        self.base_current_vector = np.array([0.0, 0.0])
        self.base_wind_vector = np.array([0.0, 0.0])
        
        # 初始化基类
        super().__init__(
            map_size=map_size,
            num_obstacles=num_obstacles,
            obstacle_radius_range=obstacle_radius_range,
            goal_threshold=goal_threshold,
            max_episode_steps=max_episode_steps,
            render_mode=render_mode,
            seed=seed
        )
        
        print(f"物理导航环境初始化完成: 物理引擎={'启用' if self.use_physics_engine else '禁用'}, "
              f"物理步长={physics_dt}s, 每环境步物理步数={physics_steps_per_env_step}")
    
    def _setup_physics_world(self) -> None:
        """设置物理世界"""
        if not self.use_physics_engine:
            return
        
        # 创建物理空间
        self.space = pymunk.Space()
        self.space.gravity = (0, 0)  # 水面导航，无重力
        
        # 设置碰撞参数
        self.space.damping = 0.95  # 全局阻尼
        
        # 创建智能体物理体
        self._create_agent_body()
        
        # 创建障碍物物理体
        self._create_obstacle_bodies()
        
        # 创建边界墙体
        self._create_boundary_walls()
    
    def _create_agent_body(self) -> None:
        """创建智能体物理体"""
        if not self.use_physics_engine:
            return
        
        # 计算转动惯量
        moment = pymunk.moment_for_circle(self.agent_mass, 0, self.agent_radius)
        
        # 创建刚体
        self.agent_body = pymunk.Body(self.agent_mass, moment)
        self.agent_body.position = tuple(self.agent_pos)
        self.agent_body.angle = self.agent_heading
        
        # 创建形状
        self.agent_shape = pymunk.Circle(self.agent_body, self.agent_radius)
        self.agent_shape.friction = 0.3
        self.agent_shape.elasticity = 0.1
        
        # 添加到物理空间
        self.space.add(self.agent_body, self.agent_shape)
    
    def _create_obstacle_bodies(self) -> None:
        """创建障碍物物理体"""
        if not self.use_physics_engine:
            return
        
        self.obstacle_bodies = []
        
        for obs_x, obs_y, obs_radius in self.obstacles:
            # 创建静态障碍物
            body = pymunk.Body(body_type=pymunk.Body.STATIC)
            body.position = (obs_x, obs_y)
            
            shape = pymunk.Circle(body, obs_radius)
            shape.friction = 0.7
            shape.elasticity = 0.2
            
            self.space.add(body, shape)
            self.obstacle_bodies.append((body, shape))
    
    def _create_boundary_walls(self) -> None:
        """创建边界墙体"""
        if not self.use_physics_engine:
            return
        
        self.wall_bodies = []
        half_size = self.map_size / 2
        wall_thickness = 1.0
        
        # 四面墙体的位置和尺寸
        walls = [
            # (x, y, width, height)
            (0, half_size + wall_thickness/2, self.map_size + 2*wall_thickness, wall_thickness),  # 上墙
            (0, -half_size - wall_thickness/2, self.map_size + 2*wall_thickness, wall_thickness), # 下墙
            (half_size + wall_thickness/2, 0, wall_thickness, self.map_size + 2*wall_thickness),  # 右墙
            (-half_size - wall_thickness/2, 0, wall_thickness, self.map_size + 2*wall_thickness)  # 左墙
        ]
        
        for x, y, width, height in walls:
            body = pymunk.Body(body_type=pymunk.Body.STATIC)
            body.position = (x, y)
            
            shape = pymunk.Poly.create_box(body, (width, height))
            shape.friction = 0.7
            shape.elasticity = 0.3
            
            self.space.add(body, shape)
            self.wall_bodies.append((body, shape))

    def _reset_environment(self) -> None:
        """重置环境状态（重写基类方法）"""
        # 调用基类重置
        super()._reset_environment()

        # 重置物理世界
        if self.use_physics_engine:
            # 清理现有物理世界
            if self.space is not None:
                self.space = None

            # 重新设置物理世界
            self._setup_physics_world()

        # 重置环境变化状态
        self.physics_step_count = 0
        self.base_current_vector = self.current_vector.copy()
        self.base_wind_vector = self.wind_vector.copy()

    def _convert_action_to_forces(self, action: np.ndarray) -> Tuple[float, float]:
        """
        将动作命令转换为物理力

        Args:
            action: [线速度命令, 角速度命令] 范围 [-1, 1]

        Returns:
            Tuple[float, float]: (推进力, 转向力矩)
        """
        # 线速度命令 -> 推进力 (牛顿)
        thrust_force = action[0] * self.max_thrust_force

        # 角速度命令 -> 转向力矩 (牛顿·米)
        steering_torque = action[1] * self.max_steering_torque

        return thrust_force, steering_torque

    def _calculate_current_force(self) -> Tuple[float, float]:
        """计算洋流力"""
        if not self.use_physics_engine:
            return (0.0, 0.0)

        # 洋流力 = 洋流向量 * 力系数 * 智能体截面积
        agent_area = np.pi * self.agent_radius ** 2
        force = self.current_vector * self.current_force_coefficient * agent_area

        return tuple(force)

    def _calculate_wind_force(self) -> Tuple[float, float]:
        """计算风力"""
        if not self.use_physics_engine:
            return (0.0, 0.0)

        # 风力 = 风向量 * 力系数 * 智能体截面积
        agent_area = np.pi * self.agent_radius ** 2
        force = self.wind_vector * self.wind_force_coefficient * agent_area

        return tuple(force)

    def _calculate_drag_force(self) -> Tuple[float, float]:
        """计算阻力"""
        if not self.use_physics_engine:
            return (0.0, 0.0)

        # 获取当前速度
        velocity = np.array([self.agent_body.velocity.x, self.agent_body.velocity.y])
        speed = np.linalg.norm(velocity)

        if speed < 1e-6:
            return (0.0, 0.0)

        # 二次阻力模型：F_drag = -velocity * drag_coefficient * |velocity|
        drag_force = -velocity * self.drag_coefficient * speed

        return tuple(drag_force)

    def _execute_action(self, action: np.ndarray) -> None:
        """执行动作并更新物理状态（重写基类方法）"""
        if not self.use_physics_engine:
            # 降级为原始环境行为
            super()._execute_action(action)
            return

        # 1. 转换动作为力
        thrust_force, steering_torque = self._convert_action_to_forces(action)

        # 2. 计算环境力
        current_force = self._calculate_current_force()
        wind_force = self._calculate_wind_force()
        drag_force = self._calculate_drag_force()

        # 3. 计算推进力的方向分量
        thrust_direction = np.array([np.cos(self.agent_body.angle), np.sin(self.agent_body.angle)])
        thrust_force_vector = thrust_force * thrust_direction

        # 4. 计算总力
        total_force = (thrust_force_vector[0] + current_force[0] + wind_force[0] + drag_force[0],
                      thrust_force_vector[1] + current_force[1] + wind_force[1] + drag_force[1])

        # 5. 执行物理仿真步进
        for _ in range(self.physics_steps_per_env_step):
            # 施加力到智能体
            self.agent_body.force = total_force
            self.agent_body.torque = steering_torque

            # 更新环境参数（如果启用时空变化）
            if self.enable_temporal_variation:
                self._update_environment_factors_temporal()

            # 执行物理步进
            self.space.step(self.physics_dt)
            self.physics_step_count += 1

            # 重新计算力（因为速度变化导致阻力变化）
            if _ < self.physics_steps_per_env_step - 1:  # 最后一步不需要重新计算
                drag_force = self._calculate_drag_force()
                total_force = (thrust_force_vector[0] + current_force[0] + wind_force[0] + drag_force[0],
                              thrust_force_vector[1] + current_force[1] + wind_force[1] + drag_force[1])

        # 6. 更新环境状态变量
        self._update_agent_state_from_physics()

        # 7. 基于位置更新环境参数（如果启用空间变化）
        if self.enable_spatial_variation:
            self._update_environment_factors_spatial()

    def _update_agent_state_from_physics(self) -> None:
        """从物理引擎同步智能体状态到环境变量"""
        if not self.use_physics_engine:
            return

        # 位置
        self.agent_pos = np.array([self.agent_body.position.x, self.agent_body.position.y])

        # 速度
        velocity_vector = self.agent_body.velocity
        self.agent_velocity = velocity_vector.length

        # 角度和角速度
        self.agent_heading = normalize_angle(self.agent_body.angle)
        self.agent_angular_velocity = self.agent_body.angular_velocity

    def _update_environment_factors_spatial(self) -> None:
        """基于智能体位置更新环境参数"""
        if not self.enable_spatial_variation:
            return

        x, y = self.agent_pos

        # 基于位置的洋流变化（例如：靠近边界洋流更强）
        distance_to_center = np.linalg.norm([x, y])
        center_distance_ratio = distance_to_center / (self.map_size / 2)

        # 洋流强度随距离中心的距离增加
        current_multiplier = 1.0 + 0.5 * center_distance_ratio

        # 基于位置的风力变化（空间周期性变化）
        wind_multiplier = 1.0 + 0.3 * np.sin(x / 10) * np.cos(y / 10)

        # 应用空间变化
        self.current_vector = self.base_current_vector * current_multiplier
        self.wind_vector = self.base_wind_vector * wind_multiplier

        # 限制最大值，避免过度变化
        max_current_strength = 3.0
        max_wind_strength = 2.5

        current_strength = np.linalg.norm(self.current_vector)
        if current_strength > max_current_strength:
            self.current_vector = self.current_vector / current_strength * max_current_strength

        wind_strength = np.linalg.norm(self.wind_vector)
        if wind_strength > max_wind_strength:
            self.wind_vector = self.wind_vector / wind_strength * max_wind_strength

    def _update_environment_factors_temporal(self) -> None:
        """基于时间更新环境参数（短期波动）"""
        if not self.enable_temporal_variation:
            return

        # 短期波动：每个物理步长添加小幅随机扰动
        if self.physics_step_count % 10 == 0:  # 每10个物理步长更新一次
            # 洋流短期波动
            current_noise = np.random.normal(0, 0.02, 2)
            self.current_vector += current_noise

            # 风力短期波动
            wind_noise = np.random.normal(0, 0.015, 2)
            self.wind_vector += wind_noise

        # 中期趋势：每100个物理步长调整基准值
        if self.physics_step_count % 100 == 0:
            # 基准洋流缓慢变化
            base_current_change = np.random.normal(0, 0.01, 2)
            self.base_current_vector += base_current_change

            # 基准风力缓慢变化
            base_wind_change = np.random.normal(0, 0.008, 2)
            self.base_wind_vector += base_wind_change

            # 限制基准值范围
            base_current_strength = np.linalg.norm(self.base_current_vector)
            if base_current_strength > 2.5:
                self.base_current_vector = self.base_current_vector / base_current_strength * 2.5
            elif base_current_strength < 0.3:
                self.base_current_vector = self.base_current_vector / base_current_strength * 0.3

            base_wind_strength = np.linalg.norm(self.base_wind_vector)
            if base_wind_strength > 2.0:
                self.base_wind_vector = self.base_wind_vector / base_wind_strength * 2.0
            elif base_wind_strength < 0.2:
                self.base_wind_vector = self.base_wind_vector / base_wind_strength * 0.2

    def _calculate_environment_reward(self, action: np.ndarray) -> float:
        """
        计算环境影响奖励（增强版）

        在原有奖励基础上，加入侧向力影响和物理真实性奖励
        """
        # 原有的对齐奖励
        agent_direction = np.array([np.cos(self.agent_heading), np.sin(self.agent_heading)])
        current_alignment = np.dot(self.current_vector, agent_direction)
        wind_alignment = np.dot(self.wind_vector, agent_direction)

        # 基础对齐奖励
        alignment_reward = 0.5 * current_alignment + 0.3 * wind_alignment

        # 新增：侧向力影响奖励
        agent_perpendicular = np.array([-np.sin(self.agent_heading), np.cos(self.agent_heading)])
        current_lateral = np.dot(self.current_vector, agent_perpendicular)
        wind_lateral = np.dot(self.wind_vector, agent_perpendicular)

        # 侧向力越大，控制难度越高，给予额外奖励
        lateral_challenge_reward = 0.1 * (abs(current_lateral) + abs(wind_lateral))

        # 新增：速度效率奖励（鼓励利用环境力）
        if self.use_physics_engine and hasattr(self, 'agent_body'):
            velocity = np.array([self.agent_body.velocity.x, self.agent_body.velocity.y])
            speed = np.linalg.norm(velocity)

            # 如果速度方向与有利环境力对齐，给予奖励
            if speed > 0.1:
                velocity_direction = velocity / speed
                beneficial_force = self.current_vector + self.wind_vector
                if np.linalg.norm(beneficial_force) > 0.1:
                    beneficial_direction = beneficial_force / np.linalg.norm(beneficial_force)
                    efficiency_reward = 0.2 * np.dot(velocity_direction, beneficial_direction)
                else:
                    efficiency_reward = 0.0
            else:
                efficiency_reward = 0.0
        else:
            efficiency_reward = 0.0

        # 能量消耗惩罚（基于实际施加的力）
        energy_penalty = self._calculate_energy_penalty(action)

        # 综合奖励
        total_reward = (alignment_reward + lateral_challenge_reward +
                       efficiency_reward - energy_penalty)

        return total_reward

    def _calculate_energy_penalty(self, action: np.ndarray) -> float:
        """计算能量消耗惩罚（增强版）"""
        if self.use_physics_engine and hasattr(self, 'agent_body'):
            # 基于实际施加的力计算能量消耗
            force_magnitude = np.linalg.norm([self.agent_body.force.x, self.agent_body.force.y])
            torque_magnitude = abs(self.agent_body.torque)

            # 能量消耗与力的平方成正比
            force_energy = (force_magnitude / self.max_thrust_force) ** 2
            torque_energy = (torque_magnitude / self.max_steering_torque) ** 2

            return 0.1 * (force_energy + 0.5 * torque_energy)
        else:
            # 降级为原始计算方式
            # 基于动作幅度计算能量消耗
            linear_velocity = action[0]
            angular_velocity = action[1]
            energy_consumption = (linear_velocity**2 + 0.5 * angular_velocity**2)
            return 0.1 * energy_consumption

    def _is_collision_with_obstacles(self) -> bool:
        """检查是否与障碍物碰撞（增强版）"""
        if self.use_physics_engine:
            # 使用物理引擎的碰撞检测
            for arbiter in self.space.arbiters:
                if self.agent_shape in [arbiter.shapes[0], arbiter.shapes[1]]:
                    # 检查是否与障碍物碰撞（排除边界墙）
                    other_shape = arbiter.shapes[1] if arbiter.shapes[0] == self.agent_shape else arbiter.shapes[0]

                    # 检查是否是障碍物（通过检查是否在obstacle_bodies中）
                    for body, shape in self.obstacle_bodies:
                        if shape == other_shape:
                            return True
            return False
        else:
            # 降级为原始碰撞检测
            for obs_x, obs_y, obs_radius in self.obstacles:
                distance = calculate_distance(self.agent_pos, np.array([obs_x, obs_y]))
                if distance < (obs_radius + self.agent_radius):
                    return True
            return False

    def _is_out_of_bounds(self) -> bool:
        """检查是否超出边界"""
        if self.use_physics_engine:
            # 物理引擎会自动处理边界碰撞，这里检查是否与边界墙碰撞
            for arbiter in self.space.arbiters:
                if self.agent_shape in [arbiter.shapes[0], arbiter.shapes[1]]:
                    other_shape = arbiter.shapes[1] if arbiter.shapes[0] == self.agent_shape else arbiter.shapes[0]

                    # 检查是否是边界墙
                    for body, shape in self.wall_bodies:
                        if shape == other_shape:
                            return True
            return False
        else:
            # 降级为原始边界检测
            half_size = self.map_size / 2
            return (abs(self.agent_pos[0]) > half_size or
                   abs(self.agent_pos[1]) > half_size)

    def _get_radar_distances(self) -> np.ndarray:
        """获取雷达距离数据（增强版）"""
        if self.use_physics_engine:
            # 使用物理引擎的射线检测
            distances = np.full(len(self.radar_angles), self.radar_range)

            for i, angle in enumerate(self.radar_angles):
                # 计算射线方向
                ray_angle = self.agent_heading + angle
                ray_direction = np.array([np.cos(ray_angle), np.sin(ray_angle)])

                # 射线起点和终点
                start_point = self.agent_pos
                end_point = start_point + ray_direction * self.radar_range

                # 执行射线检测
                query = self.space.segment_query_first(
                    tuple(start_point), tuple(end_point), 0,
                    pymunk.ShapeFilter()
                )

                if query:
                    # 计算碰撞点距离
                    hit_point = np.array([query.point.x, query.point.y])
                    distance = np.linalg.norm(hit_point - start_point)
                    distances[i] = min(distance, self.radar_range)

            return distances
        else:
            # 降级为原始雷达检测
            return super()._get_radar_distances()

    def get_physics_info(self) -> Dict[str, Any]:
        """获取物理引擎相关信息（用于调试和分析）"""
        if not self.use_physics_engine or not hasattr(self, 'agent_body'):
            return {}

        return {
            'physics_enabled': True,
            'agent_position': [self.agent_body.position.x, self.agent_body.position.y],
            'agent_velocity': [self.agent_body.velocity.x, self.agent_body.velocity.y],
            'agent_speed': self.agent_body.velocity.length,
            'agent_angle': self.agent_body.angle,
            'agent_angular_velocity': self.agent_body.angular_velocity,
            'applied_force': [self.agent_body.force.x, self.agent_body.force.y],
            'applied_torque': self.agent_body.torque,
            'physics_step_count': self.physics_step_count,
            'current_vector': self.current_vector.tolist(),
            'wind_vector': self.wind_vector.tolist(),
            'base_current_vector': self.base_current_vector.tolist(),
            'base_wind_vector': self.base_wind_vector.tolist()
        }

    def set_physics_parameters(self, **kwargs) -> None:
        """动态设置物理参数（用于实验和调优）"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"物理参数更新: {key} = {value}")
            else:
                print(f"警告: 未知参数 {key}")

    def get_environment_complexity_metrics(self) -> Dict[str, float]:
        """获取环境复杂度指标"""
        metrics = {}

        # 环境力强度
        current_strength = np.linalg.norm(self.current_vector)
        wind_strength = np.linalg.norm(self.wind_vector)
        metrics['current_strength'] = current_strength
        metrics['wind_strength'] = wind_strength
        metrics['total_env_force'] = current_strength + wind_strength

        # 环境变化程度
        if hasattr(self, 'base_current_vector'):
            current_variation = np.linalg.norm(self.current_vector - self.base_current_vector)
            wind_variation = np.linalg.norm(self.wind_vector - self.base_wind_vector)
            metrics['current_variation'] = current_variation
            metrics['wind_variation'] = wind_variation

        # 控制难度（基于侧向力）
        if self.agent_velocity > 0.1:
            agent_direction = np.array([np.cos(self.agent_heading), np.sin(self.agent_heading)])
            agent_perpendicular = np.array([-np.sin(self.agent_heading), np.cos(self.agent_heading)])

            lateral_current = abs(np.dot(self.current_vector, agent_perpendicular))
            lateral_wind = abs(np.dot(self.wind_vector, agent_perpendicular))
            metrics['lateral_force_difficulty'] = lateral_current + lateral_wind
        else:
            metrics['lateral_force_difficulty'] = 0.0

        return metrics
