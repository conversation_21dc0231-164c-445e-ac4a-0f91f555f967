"""
经验回放缓冲区模块

实现高效的经验回放缓冲区，支持多进程环境的数据收集和批量采样。
针对Multi-Critic SAC算法进行了优化，支持分解奖励的存储。

作者: Multi-Critic SAC Team
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union, NamedTuple
from collections import deque
import threading
import random


class Experience(NamedTuple):
    """经验数据结构"""
    state: np.ndarray
    action: np.ndarray
    reward: float
    next_state: np.ndarray
    done: bool
    # 分解奖励
    obstacle_reward: float
    guidance_reward: float
    environment_reward: float


class ReplayBuffer:
    """
    经验回放缓冲区
    
    支持高效的经验存储和批量采样，针对Multi-Critic SAC算法优化。
    """
    
    def __init__(
        self,
        capacity: int,
        state_dim: int,
        action_dim: int,
        device: torch.device = torch.device("cpu"),
        n_envs: int = 1
    ):
        """
        初始化经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            state_dim: 状态维度
            action_dim: 动作维度
            device: 计算设备
            n_envs: 并行环境数量
        """
        self.capacity = capacity
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.device = device
        self.n_envs = n_envs
        
        # 初始化存储数组
        self._init_storage()
        
        # 索引管理
        self.ptr = 0  # 当前写入位置
        self.size = 0  # 当前存储的经验数量
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        print(f"经验回放缓冲区初始化完成: 容量={capacity}, 状态维度={state_dim}, 动作维度={action_dim}")
    
    def _init_storage(self) -> None:
        """初始化存储数组"""
        # 状态和动作
        self.states = np.zeros((self.capacity, self.state_dim), dtype=np.float32)
        self.actions = np.zeros((self.capacity, self.action_dim), dtype=np.float32)
        self.next_states = np.zeros((self.capacity, self.state_dim), dtype=np.float32)
        
        # 奖励
        self.rewards = np.zeros(self.capacity, dtype=np.float32)
        self.obstacle_rewards = np.zeros(self.capacity, dtype=np.float32)
        self.guidance_rewards = np.zeros(self.capacity, dtype=np.float32)
        self.environment_rewards = np.zeros(self.capacity, dtype=np.float32)
        
        # 终止标志
        self.dones = np.zeros(self.capacity, dtype=np.bool_)
    
    def add(
        self,
        state: np.ndarray,
        action: np.ndarray,
        reward: float,
        next_state: np.ndarray,
        done: bool,
        obstacle_reward: float = 0.0,
        guidance_reward: float = 0.0,
        environment_reward: float = 0.0
    ) -> None:
        """
        添加单个经验
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 总奖励
            next_state: 下一状态
            done: 是否终止
            obstacle_reward: 避障奖励
            guidance_reward: 引导奖励
            environment_reward: 环境奖励
        """
        with self._lock:
            # 存储经验
            self.states[self.ptr] = state
            self.actions[self.ptr] = action
            self.rewards[self.ptr] = reward
            self.next_states[self.ptr] = next_state
            self.dones[self.ptr] = done
            
            # 存储分解奖励
            self.obstacle_rewards[self.ptr] = obstacle_reward
            self.guidance_rewards[self.ptr] = guidance_reward
            self.environment_rewards[self.ptr] = environment_reward
            
            # 更新指针和大小
            self.ptr = (self.ptr + 1) % self.capacity
            self.size = min(self.size + 1, self.capacity)
    
    def add_batch(
        self,
        states: np.ndarray,
        actions: np.ndarray,
        rewards: np.ndarray,
        next_states: np.ndarray,
        dones: np.ndarray,
        obstacle_rewards: Optional[np.ndarray] = None,
        guidance_rewards: Optional[np.ndarray] = None,
        environment_rewards: Optional[np.ndarray] = None
    ) -> None:
        """
        批量添加经验
        
        Args:
            states: 状态批次
            actions: 动作批次
            rewards: 奖励批次
            next_states: 下一状态批次
            dones: 终止标志批次
            obstacle_rewards: 避障奖励批次
            guidance_rewards: 引导奖励批次
            environment_rewards: 环境奖励批次
        """
        batch_size = len(states)
        
        # 默认分解奖励为0
        if obstacle_rewards is None:
            obstacle_rewards = np.zeros(batch_size)
        if guidance_rewards is None:
            guidance_rewards = np.zeros(batch_size)
        if environment_rewards is None:
            environment_rewards = np.zeros(batch_size)
        
        with self._lock:
            # 计算写入范围
            end_ptr = (self.ptr + batch_size) % self.capacity
            
            if end_ptr > self.ptr:
                # 连续写入
                self.states[self.ptr:end_ptr] = states
                self.actions[self.ptr:end_ptr] = actions
                self.rewards[self.ptr:end_ptr] = rewards
                self.next_states[self.ptr:end_ptr] = next_states
                self.dones[self.ptr:end_ptr] = dones
                self.obstacle_rewards[self.ptr:end_ptr] = obstacle_rewards
                self.guidance_rewards[self.ptr:end_ptr] = guidance_rewards
                self.environment_rewards[self.ptr:end_ptr] = environment_rewards
            else:
                # 环形写入
                first_part = self.capacity - self.ptr
                self.states[self.ptr:] = states[:first_part]
                self.states[:end_ptr] = states[first_part:]
                
                self.actions[self.ptr:] = actions[:first_part]
                self.actions[:end_ptr] = actions[first_part:]
                
                self.rewards[self.ptr:] = rewards[:first_part]
                self.rewards[:end_ptr] = rewards[first_part:]
                
                self.next_states[self.ptr:] = next_states[:first_part]
                self.next_states[:end_ptr] = next_states[first_part:]
                
                self.dones[self.ptr:] = dones[:first_part]
                self.dones[:end_ptr] = dones[first_part:]
                
                self.obstacle_rewards[self.ptr:] = obstacle_rewards[:first_part]
                self.obstacle_rewards[:end_ptr] = obstacle_rewards[first_part:]
                
                self.guidance_rewards[self.ptr:] = guidance_rewards[:first_part]
                self.guidance_rewards[:end_ptr] = guidance_rewards[first_part:]
                
                self.environment_rewards[self.ptr:] = environment_rewards[:first_part]
                self.environment_rewards[:end_ptr] = environment_rewards[first_part:]
            
            # 更新指针和大小
            self.ptr = end_ptr
            self.size = min(self.size + batch_size, self.capacity)
    
    def sample(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """
        随机采样批次数据
        
        Args:
            batch_size: 批次大小
            
        Returns:
            Dict[str, torch.Tensor]: 采样的批次数据
            
        Raises:
            ValueError: 缓冲区中的数据不足
        """
        if self.size < batch_size:
            raise ValueError(f"缓冲区数据不足: 需要{batch_size}, 当前{self.size}")
        
        with self._lock:
            # 随机采样索引
            indices = np.random.choice(self.size, batch_size, replace=False)
            
            # 提取数据并转换为张量
            batch = {
                'states': torch.FloatTensor(self.states[indices]).to(self.device),
                'actions': torch.FloatTensor(self.actions[indices]).to(self.device),
                'rewards': torch.FloatTensor(self.rewards[indices]).to(self.device),
                'next_states': torch.FloatTensor(self.next_states[indices]).to(self.device),
                'dones': torch.BoolTensor(self.dones[indices]).to(self.device),
                'obstacle_rewards': torch.FloatTensor(self.obstacle_rewards[indices]).to(self.device),
                'guidance_rewards': torch.FloatTensor(self.guidance_rewards[indices]).to(self.device),
                'environment_rewards': torch.FloatTensor(self.environment_rewards[indices]).to(self.device),
            }
            
            return batch
    
    def sample_sequential(self, batch_size: int, sequence_length: int) -> Dict[str, torch.Tensor]:
        """
        采样连续序列数据（用于RNN等需要序列的模型）
        
        Args:
            batch_size: 批次大小
            sequence_length: 序列长度
            
        Returns:
            Dict[str, torch.Tensor]: 序列批次数据
        """
        if self.size < batch_size * sequence_length:
            raise ValueError(f"缓冲区数据不足以采样序列")
        
        with self._lock:
            sequences = []
            
            for _ in range(batch_size):
                # 随机选择序列起始位置
                start_idx = np.random.randint(0, self.size - sequence_length + 1)
                end_idx = start_idx + sequence_length
                
                sequence = {
                    'states': self.states[start_idx:end_idx],
                    'actions': self.actions[start_idx:end_idx],
                    'rewards': self.rewards[start_idx:end_idx],
                    'next_states': self.next_states[start_idx:end_idx],
                    'dones': self.dones[start_idx:end_idx],
                    'obstacle_rewards': self.obstacle_rewards[start_idx:end_idx],
                    'guidance_rewards': self.guidance_rewards[start_idx:end_idx],
                    'environment_rewards': self.environment_rewards[start_idx:end_idx],
                }
                sequences.append(sequence)
            
            # 转换为张量
            batch = {}
            for key in sequences[0].keys():
                batch[key] = torch.FloatTensor(
                    np.stack([seq[key] for seq in sequences])
                ).to(self.device)
            
            return batch
    
    def get_statistics(self) -> Dict[str, float]:
        """
        获取缓冲区统计信息
        
        Returns:
            Dict[str, float]: 统计信息
        """
        if self.size == 0:
            return {}
        
        with self._lock:
            stats = {
                'size': self.size,
                'capacity': self.capacity,
                'utilization': self.size / self.capacity,
                'mean_reward': np.mean(self.rewards[:self.size]),
                'std_reward': np.std(self.rewards[:self.size]),
                'mean_obstacle_reward': np.mean(self.obstacle_rewards[:self.size]),
                'mean_guidance_reward': np.mean(self.guidance_rewards[:self.size]),
                'mean_environment_reward': np.mean(self.environment_rewards[:self.size]),
                'done_rate': np.mean(self.dones[:self.size]),
            }
            
            return stats
    
    def clear(self) -> None:
        """清空缓冲区"""
        with self._lock:
            self.ptr = 0
            self.size = 0
            self._init_storage()
    
    def is_ready(self, min_size: int) -> bool:
        """
        检查缓冲区是否准备好进行采样
        
        Args:
            min_size: 最小数据量
            
        Returns:
            bool: 是否准备好
        """
        return self.size >= min_size
    
    def __len__(self) -> int:
        """返回当前存储的经验数量"""
        return self.size
    
    def __repr__(self) -> str:
        """返回缓冲区的字符串表示"""
        return (f"ReplayBuffer(capacity={self.capacity}, size={self.size}, "
                f"state_dim={self.state_dim}, action_dim={self.action_dim})")
