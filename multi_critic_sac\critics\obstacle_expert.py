"""
避障专家模块

实现避障专家评论家组，专门负责评估和优化避障行为。
根据智能体与障碍物的距离计算奖励，惩罚碰撞风险，奖励安全行为。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, TYPE_CHECKING
from multi_critic_sac.networks.critic import DoubleCritic, EnsembleCritic
from multi_critic_sac.utils.common import calculate_distance

if TYPE_CHECKING:
    from multi_critic_sac.networks.actor import Actor


class ObstacleExpert(nn.Module):
    """
    避障专家评论家组
    
    专门负责评估避障行为，包括：
    - 碰撞检测和惩罚
    - 安全距离维护
    - 避障路径优化
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        n_critics: int = 2,
        collision_penalty: float = -50.0,
        safety_threshold: float = 2.0,
        distance_scale: float = 10.0,
        radar_points: int = 36,
        device: torch.device = torch.device("cpu")
    ):
        """
        初始化避障专家
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小
            activation: 激活函数
            n_critics: Critic网络数量
            collision_penalty: 碰撞惩罚
            safety_threshold: 安全距离阈值
            distance_scale: 距离奖励缩放因子
            radar_points: 雷达数据点数
            device: 计算设备
        """
        super(ObstacleExpert, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.collision_penalty = collision_penalty
        self.safety_threshold = safety_threshold
        self.distance_scale = distance_scale
        self.radar_points = radar_points
        self.device = device
        
        # 创建双Critic网络
        self.critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 目标网络
        self.target_critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 初始化目标网络
        self.target_critic.load_state_dict(self.critic.state_dict())
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.critic.parameters(), lr=3e-4)
        
        print(f"避障专家初始化完成: 碰撞惩罚={collision_penalty}, "
              f"安全阈值={safety_threshold}, 距离缩放={distance_scale}")
    
    def extract_radar_data(self, state: torch.Tensor) -> torch.Tensor:
        """
        从状态中提取雷达数据
        
        Args:
            state: 完整状态 [batch_size, state_dim]
            
        Returns:
            torch.Tensor: 雷达数据 [batch_size, radar_points]
        """
        # 假设雷达数据在状态的前radar_points维
        return state[:, :self.radar_points]
    

    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (Q1值, Q2值)
        """
        return self.critic(state, action)
    
    def target_forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        目标网络前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 目标Q值（取最小值）
        """
        return self.target_critic.min_q(state, action)
    
    def update(self, batch: Dict[str, torch.Tensor], gamma: float = 0.99, actor: Optional['Actor'] = None) -> Dict[str, float]:
        """
        更新避障专家网络

        使用环境提供的避障分解奖励进行训练。
        按照SAC算法标准，使用Actor网络从下一状态采样动作计算目标Q值。

        Args:
            batch: 训练批次数据
            gamma: 折扣因子
            actor: Actor网络，用于从下一状态采样动作

        Returns:
            Dict[str, float]: 训练指标
        """
        if actor is None:
            raise ValueError("Actor网络不能为None，SAC算法需要Actor网络计算目标Q值")
        states = batch['states']
        actions = batch['actions']
        obstacle_rewards = batch['obstacle_rewards']
        next_states = batch['next_states']
        dones = batch['dones']
        
        # 计算当前Q值
        q1, q2 = self.critic(states, actions)
        
        # 计算目标Q值（按照SAC算法标准实现）
        with torch.no_grad():
            # ✅ 正确：使用Actor网络从下一状态采样动作
            next_actions, _ = actor.sample(next_states)
            target_q = self.target_critic.min_q(next_states, next_actions)

            # 🔧 修复维度不匹配：确保奖励张量维度与Q值一致
            obstacle_rewards_reshaped = obstacle_rewards.unsqueeze(-1)  # [batch_size] -> [batch_size, 1]
            dones_reshaped = dones.float().unsqueeze(-1)  # [batch_size] -> [batch_size, 1]

            target_q = obstacle_rewards_reshaped + gamma * (1 - dones_reshaped) * target_q
        
        # 计算损失
        q1_loss = nn.MSELoss()(q1, target_q)
        q2_loss = nn.MSELoss()(q2, target_q)
        total_loss = q1_loss + q2_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 返回训练指标
        metrics = {
            'obstacle_q1_loss': q1_loss.item(),
            'obstacle_q2_loss': q2_loss.item(),
            'obstacle_total_loss': total_loss.item(),
            'obstacle_mean_q1': q1.mean().item(),
            'obstacle_mean_q2': q2.mean().item(),
            'obstacle_mean_reward': obstacle_rewards.mean().item()
        }
        
        return metrics
    
    def soft_update_target(self, tau: float = 0.005) -> None:
        """
        软更新目标网络
        
        Args:
            tau: 软更新系数
        """
        for target_param, param in zip(self.target_critic.parameters(), 
                                     self.critic.parameters()):
            target_param.data.copy_(
                tau * param.data + (1.0 - tau) * target_param.data
            )
    
    def get_q_values(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取Q值（用于融合）
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: Q值（取最小值）
        """
        return self.critic.min_q(state, action)
    
    def analyze_obstacle_situation(self, state: torch.Tensor) -> Dict[str, float]:
        """
        分析当前避障情况
        
        Args:
            state: 输入状态
            
        Returns:
            Dict[str, float]: 避障分析结果
        """
        radar_data = self.extract_radar_data(state)
        
        # 计算统计信息
        min_distance = torch.min(radar_data, dim=1)[0]
        mean_distance = torch.mean(radar_data, dim=1)
        
        # 计算危险区域数量（距离小于安全阈值的雷达点）
        danger_points = torch.sum(radar_data < self.safety_threshold, dim=1)
        
        analysis = {
            'min_obstacle_distance': min_distance.mean().item(),
            'mean_obstacle_distance': mean_distance.mean().item(),
            'danger_points_ratio': (danger_points.float() / self.radar_points).mean().item(),
            'collision_risk': (min_distance < 0.5).float().mean().item()
        }
        
        return analysis
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'critic_state_dict': self.critic.state_dict(),
            'target_critic_state_dict': self.target_critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'collision_penalty': self.collision_penalty,
                'safety_threshold': self.safety_threshold,
                'distance_scale': self.distance_scale,
                'radar_points': self.radar_points
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.target_critic.load_state_dict(checkpoint['target_critic_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    def __repr__(self) -> str:
        """返回避障专家的字符串表示"""
        return (f"ObstacleExpert(collision_penalty={self.collision_penalty}, "
                f"safety_threshold={self.safety_threshold}, "
                f"distance_scale={self.distance_scale})")
