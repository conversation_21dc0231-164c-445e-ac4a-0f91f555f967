"""
Actor网络模块

实现SAC算法中的Actor网络，输出连续动作的均值和标准差。
支持重参数化技巧和动作采样。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal
import numpy as np
from typing import Tuple, Optional, List
from multi_critic_sac.utils.common import get_activation_fn


class Actor(nn.Module):
    """
    Actor网络
    
    输出连续动作空间的策略分布参数（均值和对数标准差）。
    使用重参数化技巧进行动作采样。
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        output_activation: str = "tanh",
        log_std_min: float = -20.0,
        log_std_max: float = 2.0,
        epsilon: float = 1e-6
    ):
        """
        初始化Actor网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小列表
            activation: 激活函数名称
            output_activation: 输出激活函数名称
            log_std_min: 对数标准差最小值
            log_std_max: 对数标准差最大值
            epsilon: 数值稳定性常数
        """
        super(Actor, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.log_std_min = log_std_min
        self.log_std_max = log_std_max
        self.epsilon = epsilon
        
        # 获取激活函数
        self.activation_fn = get_activation_fn(activation)
        self.output_activation_fn = get_activation_fn(output_activation)
        
        # 构建网络层
        self.layers = nn.ModuleList()
        
        # 输入层
        prev_size = state_dim
        for hidden_size in hidden_sizes:
            self.layers.append(nn.Linear(prev_size, hidden_size))
            prev_size = hidden_size
        
        # 输出层：均值和对数标准差
        self.mean_layer = nn.Linear(prev_size, action_dim)
        self.log_std_layer = nn.Linear(prev_size, action_dim)
        
        # 初始化权重
        self._init_weights()
        
        print(f"Actor网络初始化完成: 状态维度={state_dim}, 动作维度={action_dim}, "
              f"隐藏层={hidden_sizes}")
    
    def _init_weights(self) -> None:
        """初始化网络权重"""
        for layer in self.layers:
            nn.init.xavier_uniform_(layer.weight)
            nn.init.constant_(layer.bias, 0.0)
        
        # 输出层使用较小的初始化
        nn.init.uniform_(self.mean_layer.weight, -3e-3, 3e-3)
        nn.init.uniform_(self.mean_layer.bias, -3e-3, 3e-3)
        nn.init.uniform_(self.log_std_layer.weight, -3e-3, 3e-3)
        nn.init.uniform_(self.log_std_layer.bias, -3e-3, 3e-3)
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态 [batch_size, state_dim]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (均值, 对数标准差)
        """
        x = state
        
        # 通过隐藏层
        for layer in self.layers:
            x = self.activation_fn(layer(x))
        
        # 计算均值和对数标准差
        mean = self.mean_layer(x)
        log_std = self.log_std_layer(x)
        
        # 限制对数标准差范围
        log_std = torch.clamp(log_std, self.log_std_min, self.log_std_max)
        
        return mean, log_std
    
    def sample(self, state: torch.Tensor, 
               deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        采样动作
        
        Args:
            state: 输入状态
            deterministic: 是否使用确定性策略
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (动作, 对数概率)
        """
        mean, log_std = self.forward(state)
        
        if deterministic:
            # 确定性策略：直接使用均值
            action = torch.tanh(mean)
            log_prob = None
        else:
            # 随机策略：从正态分布采样
            std = log_std.exp()
            normal = Normal(mean, std)
            
            # 重参数化采样
            x_t = normal.rsample()  # 使用rsample以支持梯度传播
            action = torch.tanh(x_t)
            
            # 计算对数概率（考虑tanh变换的雅可比行列式）
            log_prob = normal.log_prob(x_t)
            # 修正tanh变换的对数概率
            log_prob -= torch.log(1 - action.pow(2) + self.epsilon)
            log_prob = log_prob.sum(dim=-1, keepdim=True)
        
        return action, log_prob
    
    def log_prob(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        计算给定状态和动作的对数概率
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 对数概率
        """
        mean, log_std = self.forward(state)
        std = log_std.exp()
        
        # 反向tanh变换
        action_clamped = torch.clamp(action, -1 + self.epsilon, 1 - self.epsilon)
        x_t = torch.atanh(action_clamped)
        
        # 计算正态分布的对数概率
        normal = Normal(mean, std)
        log_prob = normal.log_prob(x_t)
        
        # 修正tanh变换的雅可比行列式
        log_prob -= torch.log(1 - action_clamped.pow(2) + self.epsilon)
        log_prob = log_prob.sum(dim=-1, keepdim=True)
        
        return log_prob
    
    def get_action(self, state: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """
        获取动作（用于环境交互）
        
        Args:
            state: 输入状态（numpy数组）
            deterministic: 是否使用确定性策略
            
        Returns:
            np.ndarray: 动作
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            if next(self.parameters()).is_cuda:
                state_tensor = state_tensor.cuda()
            
            action, _ = self.sample(state_tensor, deterministic)
            return action.cpu().numpy().flatten()
    
    def entropy(self, state: torch.Tensor) -> torch.Tensor:
        """
        计算策略熵
        
        Args:
            state: 输入状态
            
        Returns:
            torch.Tensor: 策略熵
        """
        _, log_std = self.forward(state)
        std = log_std.exp()
        
        # 正态分布的熵：0.5 * log(2π * e * σ²)
        entropy = 0.5 * (1 + torch.log(2 * np.pi * std.pow(2)))
        return entropy.sum(dim=-1, keepdim=True)
    
    def get_parameters_count(self) -> int:
        """获取网络参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存网络检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'state_dict': self.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'log_std_min': self.log_std_min,
                'log_std_max': self.log_std_max,
                'epsilon': self.epsilon
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载网络检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location='cpu')
        self.load_state_dict(checkpoint['state_dict'])
    
    def __repr__(self) -> str:
        """返回网络的字符串表示"""
        param_count = self.get_parameters_count()
        return (f"Actor(state_dim={self.state_dim}, action_dim={self.action_dim}, "
                f"parameters={param_count})")
