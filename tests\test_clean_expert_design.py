"""
测试清理后的专家网络设计

验证：
1. 专家网络专注于Q值估计，不包含奖励计算
2. 奖励计算统一在环境中进行
3. 分解奖励正确传递给专家网络
4. 职责分离清晰
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from multi_critic_sac.critics.obstacle_expert import ObstacleExpert
from multi_critic_sac.critics.guidance_expert import GuidanceExpert
from multi_critic_sac.critics.environment_expert import EnvironmentExpert


def test_clean_expert_design():
    """测试清理后的专家网络设计"""
    print("开始测试清理后的专家网络设计...")
    
    try:
        device = torch.device("cpu")
        batch_size = 4
        state_dim = 45  # 更新后的状态维度
        action_dim = 2
        
        # 创建专家网络
        obstacle_expert = ObstacleExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        guidance_expert = GuidanceExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        environment_expert = EnvironmentExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=device
        )
        
        print("测试1: 验证专家网络不包含奖励计算方法")
        
        # 检查避障专家
        assert not hasattr(obstacle_expert, 'calculate_obstacle_reward'), \
            "避障专家不应该包含奖励计算方法"
        print("✓ 避障专家不包含奖励计算方法")
        
        # 检查引导专家
        assert not hasattr(guidance_expert, 'calculate_guidance_reward'), \
            "引导专家不应该包含奖励计算方法"
        print("✓ 引导专家不包含奖励计算方法")
        
        # 检查环境影响专家
        assert not hasattr(environment_expert, 'calculate_environment_reward'), \
            "环境影响专家不应该包含奖励计算方法"
        print("✓ 环境影响专家不包含奖励计算方法")
        
        print("测试2: 验证专家网络的核心功能（Q值估计）")
        
        # 创建测试数据
        states = torch.randn(batch_size, state_dim, device=device)
        actions = torch.randn(batch_size, action_dim, device=device)
        
        # 测试前向传播
        q1, q2 = obstacle_expert.forward(states, actions)
        assert q1.shape == (batch_size, 1), f"Q1形状错误: {q1.shape}"
        assert q2.shape == (batch_size, 1), f"Q2形状错误: {q2.shape}"
        print("✓ 避障专家Q值估计功能正常")
        
        q1, q2 = guidance_expert.forward(states, actions)
        assert q1.shape == (batch_size, 1), f"Q1形状错误: {q1.shape}"
        assert q2.shape == (batch_size, 1), f"Q2形状错误: {q2.shape}"
        print("✓ 引导专家Q值估计功能正常")
        
        q1, q2 = environment_expert.forward(states, actions)
        assert q1.shape == (batch_size, 1), f"Q1形状错误: {q1.shape}"
        assert q2.shape == (batch_size, 1), f"Q2形状错误: {q2.shape}"
        print("✓ 环境影响专家Q值估计功能正常")
        
        print("测试3: 验证专家网络使用环境提供的分解奖励")
        
        # 创建包含分解奖励的批次数据
        batch = {
            'states': states,
            'actions': actions,
            'rewards': torch.randn(batch_size),  # 总奖励
            'next_states': torch.randn(batch_size, state_dim, device=device),
            'dones': torch.zeros(batch_size, dtype=torch.bool, device=device),
            # 分解奖励（模拟环境提供）
            'obstacle_rewards': torch.randn(batch_size),
            'guidance_rewards': torch.randn(batch_size),
            'environment_rewards': torch.randn(batch_size)
        }
        
        # 测试专家网络更新
        obstacle_metrics = obstacle_expert.update(batch, gamma=0.99)
        assert 'obstacle_total_loss' in obstacle_metrics, "缺少避障专家损失指标"
        print("✓ 避障专家使用环境提供的obstacle_rewards")
        
        guidance_metrics = guidance_expert.update(batch, gamma=0.99)
        assert 'guidance_total_loss' in guidance_metrics, "缺少引导专家损失指标"
        print("✓ 引导专家使用环境提供的guidance_rewards")
        
        environment_metrics = environment_expert.update(batch, gamma=0.99)
        assert 'environment_total_loss' in environment_metrics, "缺少环境专家损失指标"
        print("✓ 环境影响专家使用环境提供的environment_rewards")
        
        print("测试4: 验证目标网络功能")
        
        # 测试目标网络前向传播
        target_q = obstacle_expert.target_forward(states, actions)
        assert target_q.shape == (batch_size, 1), f"目标Q值形状错误: {target_q.shape}"
        print("✓ 避障专家目标网络功能正常")
        
        target_q = guidance_expert.target_forward(states, actions)
        assert target_q.shape == (batch_size, 1), f"目标Q值形状错误: {target_q.shape}"
        print("✓ 引导专家目标网络功能正常")
        
        target_q = environment_expert.target_forward(states, actions)
        assert target_q.shape == (batch_size, 1), f"目标Q值形状错误: {target_q.shape}"
        print("✓ 环境影响专家目标网络功能正常")
        
        print("测试5: 验证软更新功能")
        
        # 获取更新前的参数
        old_params = list(obstacle_expert.target_critic.parameters())[0].clone()
        
        # 执行软更新
        obstacle_expert.soft_update_target(tau=0.1)
        
        # 获取更新后的参数
        new_params = list(obstacle_expert.target_critic.parameters())[0]
        
        # 验证参数确实发生了变化
        assert not torch.allclose(old_params, new_params), "软更新没有生效"
        print("✓ 软更新功能正常")
        
        print("测试6: 验证状态提取功能")
        
        # 测试状态提取（这些方法仍然需要，用于理解状态结构）
        radar_data = obstacle_expert.extract_radar_data(states)
        assert radar_data.shape == (batch_size, 36), f"雷达数据形状错误: {radar_data.shape}"
        print("✓ 雷达数据提取功能正常")
        
        target_vector = guidance_expert.extract_target_vector(states)
        assert target_vector.shape == (batch_size, 2), f"目标向量形状错误: {target_vector.shape}"
        print("✓ 目标向量提取功能正常")
        
        env_data = environment_expert.extract_environment_data(states)
        assert 'current_x' in env_data, "缺少洋流x分量"
        assert 'wind_y' in env_data, "缺少风力y分量"
        print("✓ 环境数据提取功能正常")
        
        print("\n🎉 所有测试通过！清理后的专家网络设计正确。")
        
        # 打印设计总结
        print("\n📋 清理后的设计总结:")
        print("1. ✅ 专家网络专注于Q值估计，不包含奖励计算")
        print("2. ✅ 奖励计算统一在环境中进行")
        print("3. ✅ 分解奖励通过batch正确传递给专家网络")
        print("4. ✅ 职责分离清晰：环境负责奖励，专家负责Q值")
        print("5. ✅ 避免了代码重复和不一致问题")
        print("6. ✅ 符合单一职责原则")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_clean_expert_design()
    sys.exit(0 if success else 1)
