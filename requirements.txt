# Multi-Critic SAC 项目依赖
# PyTorch 深度学习框架
torch==2.6.0+cu118
torchvision==0.21.0+cu118
torchaudio==2.6.0+cu118

# 强化学习相关
stable-baselines3==2.3.2
gymnasium==0.29.1
gymnasium[classic_control]==0.29.1

# 数值计算和科学计算
numpy==1.24.3
scipy==1.11.1

# 可视化和监控
tensorboard==2.15.1
matplotlib==3.7.2
seaborn==0.12.2

# 配置管理
pyyaml==6.0.1
hydra-core==1.3.2

# 工具库
tqdm==4.66.1
wandb==0.16.0

# 开发工具
pytest==7.4.0
black==23.7.0
flake8==6.0.0
mypy==1.5.1

# 其他依赖
opencv-python==********
pillow==10.0.0
