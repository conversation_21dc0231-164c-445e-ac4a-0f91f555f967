"""
通用工具函数

包含项目中常用的工具函数和辅助方法。

作者: Multi-Critic SAC Team
"""

import os
import random
import numpy as np
import torch
import torch.nn as nn
from typing import Union, Tuple, List, Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime


def set_seed(seed: int) -> None:
    """
    设置随机种子以确保实验的可重复性
    
    Args:
        seed: 随机种子值
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_device(use_cuda: bool = True, cuda_device: int = 0) -> torch.device:
    """
    获取计算设备
    
    Args:
        use_cuda: 是否使用CUDA
        cuda_device: CUDA设备编号
        
    Returns:
        torch.device: 计算设备
    """
    if use_cuda and torch.cuda.is_available():
        device = torch.device(f"cuda:{cuda_device}")
        print(f"使用GPU设备: {device}")
    else:
        device = torch.device("cpu")
        print("使用CPU设备")
    
    return device


def create_directories(paths: Union[str, List[str]]) -> None:
    """
    创建目录（如果不存在）
    
    Args:
        paths: 目录路径或路径列表
    """
    if isinstance(paths, str):
        paths = [paths]
    
    for path in paths:
        Path(path).mkdir(parents=True, exist_ok=True)


def get_activation_fn(activation_name: str) -> nn.Module:
    """
    根据名称获取激活函数
    
    Args:
        activation_name: 激活函数名称
        
    Returns:
        nn.Module: 激活函数
        
    Raises:
        ValueError: 不支持的激活函数
    """
    activation_map = {
        'relu': nn.ReLU(),
        'tanh': nn.Tanh(),
        'sigmoid': nn.Sigmoid(),
        'leaky_relu': nn.LeakyReLU(),
        'elu': nn.ELU(),
        'gelu': nn.GELU(),
        'swish': nn.SiLU(),
    }
    
    if activation_name.lower() not in activation_map:
        raise ValueError(f"不支持的激活函数: {activation_name}")
    
    return activation_map[activation_name.lower()]


def soft_update(target_net: nn.Module, source_net: nn.Module, tau: float) -> None:
    """
    软更新目标网络参数
    
    Args:
        target_net: 目标网络
        source_net: 源网络
        tau: 软更新系数
    """
    for target_param, source_param in zip(target_net.parameters(), source_net.parameters()):
        target_param.data.copy_(
            tau * source_param.data + (1.0 - tau) * target_param.data
        )


def hard_update(target_net: nn.Module, source_net: nn.Module) -> None:
    """
    硬更新目标网络参数
    
    Args:
        target_net: 目标网络
        source_net: 源网络
    """
    target_net.load_state_dict(source_net.state_dict())


def calculate_distance(pos1: np.ndarray, pos2: np.ndarray) -> float:
    """
    计算两点之间的欧几里得距离
    
    Args:
        pos1: 位置1
        pos2: 位置2
        
    Returns:
        float: 距离
    """
    return np.linalg.norm(pos1 - pos2)


def normalize_angle(angle: float) -> float:
    """
    将角度归一化到[-π, π]范围
    
    Args:
        angle: 输入角度（弧度）
        
    Returns:
        float: 归一化后的角度
    """
    while angle > np.pi:
        angle -= 2 * np.pi
    while angle < -np.pi:
        angle += 2 * np.pi
    return angle


def angle_difference(angle1: float, angle2: float) -> float:
    """
    计算两个角度之间的最小差值
    
    Args:
        angle1: 角度1（弧度）
        angle2: 角度2（弧度）
        
    Returns:
        float: 角度差值
    """
    diff = angle1 - angle2
    return normalize_angle(diff)


def polar_to_cartesian(distance: float, angle: float) -> Tuple[float, float]:
    """
    极坐标转笛卡尔坐标
    
    Args:
        distance: 距离
        angle: 角度（弧度）
        
    Returns:
        Tuple[float, float]: (x, y)坐标
    """
    x = distance * np.cos(angle)
    y = distance * np.sin(angle)
    return x, y


def cartesian_to_polar(x: float, y: float) -> Tuple[float, float]:
    """
    笛卡尔坐标转极坐标
    
    Args:
        x: x坐标
        y: y坐标
        
    Returns:
        Tuple[float, float]: (距离, 角度)
    """
    distance = np.sqrt(x**2 + y**2)
    angle = np.arctan2(y, x)
    return distance, angle


def clip_action(action: np.ndarray, action_space_low: np.ndarray, 
                action_space_high: np.ndarray) -> np.ndarray:
    """
    将动作限制在动作空间范围内
    
    Args:
        action: 输入动作
        action_space_low: 动作空间下界
        action_space_high: 动作空间上界
        
    Returns:
        np.ndarray: 限制后的动作
    """
    return np.clip(action, action_space_low, action_space_high)


def moving_average(data: List[float], window_size: int) -> List[float]:
    """
    计算移动平均
    
    Args:
        data: 数据列表
        window_size: 窗口大小
        
    Returns:
        List[float]: 移动平均结果
    """
    if len(data) < window_size:
        return data
    
    result = []
    for i in range(len(data) - window_size + 1):
        window_avg = sum(data[i:i + window_size]) / window_size
        result.append(window_avg)
    
    return result


def setup_logger(name: str, log_file: Optional[str] = None, 
                level: int = logging.INFO) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径（可选）
        level: 日志级别
        
    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了文件路径）
    if log_file:
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_timestamp() -> str:
    """
    获取当前时间戳字符串
    
    Returns:
        str: 时间戳字符串
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def save_dict_to_file(data: Dict[str, Any], file_path: str) -> None:
    """
    将字典保存到文件
    
    Args:
        data: 要保存的字典
        file_path: 文件路径
    """
    import json
    
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def load_dict_from_file(file_path: str) -> Dict[str, Any]:
    """
    从文件加载字典
    
    Args:
        file_path: 文件路径
        
    Returns:
        Dict[str, Any]: 加载的字典
    """
    import json
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)
