"""
引导专家模块

实现引导专家评论家组，专门负责评估和优化目标导向行为。
根据智能体与目标的距离变化计算奖励，引导智能体向目标移动。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, TYPE_CHECKING
from multi_critic_sac.networks.critic import DoubleCritic
from multi_critic_sac.utils.common import calculate_distance, normalize_angle

if TYPE_CHECKING:
    from multi_critic_sac.networks.actor import Actor


class GuidanceExpert(nn.Module):
    """
    引导专家评论家组
    
    专门负责评估目标导向行为，包括：
    - 目标距离优化
    - 路径效率评估
    - 到达目标奖励
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        n_critics: int = 2,
        goal_reward: float = 100.0,
        progress_scale: float = 1.0,
        time_penalty: float = -0.01,
        goal_threshold: float = 1.0,
        device: torch.device = torch.device("cpu")
    ):
        """
        初始化引导专家
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小
            activation: 激活函数
            n_critics: Critic网络数量
            goal_reward: 到达目标奖励
            progress_scale: 进度奖励缩放因子
            time_penalty: 时间惩罚
            goal_threshold: 目标到达阈值
            device: 计算设备
        """
        super(GuidanceExpert, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.goal_reward = goal_reward
        self.progress_scale = progress_scale
        self.time_penalty = time_penalty
        self.goal_threshold = goal_threshold
        self.device = device
        
        # 创建双Critic网络
        self.critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 目标网络
        self.target_critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 初始化目标网络
        self.target_critic.load_state_dict(self.critic.state_dict())
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.critic.parameters(), lr=3e-4)
        
        print(f"引导专家初始化完成: 目标奖励={goal_reward}, "
              f"进度缩放={progress_scale}, 时间惩罚={time_penalty}")
    
    def extract_target_vector(self, state: torch.Tensor) -> torch.Tensor:
        """
        从状态中提取目标向量
        
        Args:
            state: 完整状态 [batch_size, state_dim]
            
        Returns:
            torch.Tensor: 目标向量 [batch_size, 2] (距离, 角度)
        """
        # 假设目标向量在雷达数据之后的2维
        radar_points = 36  # 雷达数据点数
        return state[:, radar_points:radar_points+2]
    

    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (Q1值, Q2值)
        """
        return self.critic(state, action)
    
    def target_forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        目标网络前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 目标Q值（取最小值）
        """
        return self.target_critic.min_q(state, action)
    
    def update(self, batch: Dict[str, torch.Tensor], gamma: float = 0.99, actor: Optional['Actor'] = None) -> Dict[str, float]:
        """
        更新引导专家网络

        使用环境提供的引导分解奖励进行训练。
        按照SAC算法标准，使用Actor网络从下一状态采样动作计算目标Q值。

        Args:
            batch: 训练批次数据
            gamma: 折扣因子
            actor: Actor网络，用于从下一状态采样动作

        Returns:
            Dict[str, float]: 训练指标
        """
        if actor is None:
            raise ValueError("Actor网络不能为None，SAC算法需要Actor网络计算目标Q值")
        states = batch['states']
        actions = batch['actions']
        guidance_rewards = batch['guidance_rewards']
        next_states = batch['next_states']
        dones = batch['dones']
        
        # 计算当前Q值
        q1, q2 = self.critic(states, actions)
        
        # 计算目标Q值（按照SAC算法标准实现）
        with torch.no_grad():
            # ✅ 正确：使用Actor网络从下一状态采样动作
            next_actions, _ = actor.sample(next_states)
            target_q = self.target_critic.min_q(next_states, next_actions)

            # 🔧 修复维度不匹配：确保奖励张量维度与Q值一致
            guidance_rewards_reshaped = guidance_rewards.unsqueeze(-1)  # [batch_size] -> [batch_size, 1]
            dones_reshaped = dones.float().unsqueeze(-1)  # [batch_size] -> [batch_size, 1]

            target_q = guidance_rewards_reshaped + gamma * (1 - dones_reshaped) * target_q
        
        # 计算损失
        q1_loss = nn.MSELoss()(q1, target_q)
        q2_loss = nn.MSELoss()(q2, target_q)
        total_loss = q1_loss + q2_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 返回训练指标
        metrics = {
            'guidance_q1_loss': q1_loss.item(),
            'guidance_q2_loss': q2_loss.item(),
            'guidance_total_loss': total_loss.item(),
            'guidance_mean_q1': q1.mean().item(),
            'guidance_mean_q2': q2.mean().item(),
            'guidance_mean_reward': guidance_rewards.mean().item()
        }
        
        return metrics
    
    def soft_update_target(self, tau: float = 0.005) -> None:
        """
        软更新目标网络
        
        Args:
            tau: 软更新系数
        """
        for target_param, param in zip(self.target_critic.parameters(), 
                                     self.critic.parameters()):
            target_param.data.copy_(
                tau * param.data + (1.0 - tau) * target_param.data
            )
    
    def get_q_values(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取Q值（用于融合）
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: Q值（取最小值）
        """
        return self.critic.min_q(state, action)
    
    def analyze_guidance_situation(self, state: torch.Tensor) -> Dict[str, float]:
        """
        分析当前引导情况
        
        Args:
            state: 输入状态
            
        Returns:
            Dict[str, float]: 引导分析结果
        """
        target_vector = self.extract_target_vector(state)
        
        # 计算到目标的距离和角度
        distance_to_goal = torch.norm(target_vector, dim=1)
        angle_to_goal = torch.atan2(target_vector[:, 1], target_vector[:, 0])
        
        analysis = {
            'distance_to_goal': distance_to_goal.mean().item(),
            'angle_to_goal': angle_to_goal.mean().item(),
            'goal_reached_ratio': (distance_to_goal < self.goal_threshold).float().mean().item(),
            'close_to_goal_ratio': (distance_to_goal < 2 * self.goal_threshold).float().mean().item()
        }
        
        return analysis
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'critic_state_dict': self.critic.state_dict(),
            'target_critic_state_dict': self.target_critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'goal_reward': self.goal_reward,
                'progress_scale': self.progress_scale,
                'time_penalty': self.time_penalty,
                'goal_threshold': self.goal_threshold
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.target_critic.load_state_dict(checkpoint['target_critic_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    def __repr__(self) -> str:
        """返回引导专家的字符串表示"""
        return (f"GuidanceExpert(goal_reward={self.goal_reward}, "
                f"progress_scale={self.progress_scale}, "
                f"time_penalty={self.time_penalty})")
