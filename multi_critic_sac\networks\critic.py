"""
Critic网络模块

实现SAC算法中的Critic网络，用于估计状态-动作价值函数Q(s,a)。
支持双Q网络结构以减少过估计偏差。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional
from multi_critic_sac.utils.common import get_activation_fn


class Critic(nn.Module):
    """
    Critic网络
    
    估计状态-动作价值函数Q(s,a)。
    输入状态和动作，输出Q值。
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        output_activation: Optional[str] = None
    ):
        """
        初始化Critic网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小列表
            activation: 激活函数名称
            output_activation: 输出激活函数名称（可选）
        """
        super(Critic, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 获取激活函数
        self.activation_fn = get_activation_fn(activation)
        self.output_activation_fn = (get_activation_fn(output_activation) 
                                   if output_activation else None)
        
        # 构建网络层
        self.layers = nn.ModuleList()
        
        # 输入层：状态和动作拼接
        input_dim = state_dim + action_dim
        prev_size = input_dim
        
        for hidden_size in hidden_sizes:
            self.layers.append(nn.Linear(prev_size, hidden_size))
            prev_size = hidden_size
        
        # 输出层：单个Q值
        self.output_layer = nn.Linear(prev_size, 1)
        
        # 初始化权重
        self._init_weights()
        
        print(f"Critic网络初始化完成: 状态维度={state_dim}, 动作维度={action_dim}, "
              f"隐藏层={hidden_sizes}")
    
    def _init_weights(self) -> None:
        """初始化网络权重"""
        for layer in self.layers:
            nn.init.xavier_uniform_(layer.weight)
            nn.init.constant_(layer.bias, 0.0)
        
        # 输出层使用较小的初始化
        nn.init.uniform_(self.output_layer.weight, -3e-3, 3e-3)
        nn.init.uniform_(self.output_layer.bias, -3e-3, 3e-3)
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 输入状态 [batch_size, state_dim]
            action: 输入动作 [batch_size, action_dim]
            
        Returns:
            torch.Tensor: Q值 [batch_size, 1]
        """
        # 拼接状态和动作
        x = torch.cat([state, action], dim=-1)
        
        # 通过隐藏层
        for layer in self.layers:
            x = self.activation_fn(layer(x))
        
        # 输出层
        q_value = self.output_layer(x)
        
        # 应用输出激活函数（如果有）
        if self.output_activation_fn:
            q_value = self.output_activation_fn(q_value)
        
        return q_value
    
    def get_parameters_count(self) -> int:
        """获取网络参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存网络检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'state_dict': self.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载网络检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location='cpu')
        self.load_state_dict(checkpoint['state_dict'])
    
    def __repr__(self) -> str:
        """返回网络的字符串表示"""
        param_count = self.get_parameters_count()
        return (f"Critic(state_dim={self.state_dim}, action_dim={self.action_dim}, "
                f"parameters={param_count})")


class DoubleCritic(nn.Module):
    """
    双Critic网络
    
    包含两个独立的Critic网络，用于减少Q值过估计偏差。
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        output_activation: Optional[str] = None
    ):
        """
        初始化双Critic网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小列表
            activation: 激活函数名称
            output_activation: 输出激活函数名称（可选）
        """
        super(DoubleCritic, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 创建两个独立的Critic网络
        self.critic1 = Critic(state_dim, action_dim, hidden_sizes, 
                             activation, output_activation)
        self.critic2 = Critic(state_dim, action_dim, hidden_sizes, 
                             activation, output_activation)
        
        print(f"双Critic网络初始化完成: 状态维度={state_dim}, 动作维度={action_dim}")
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (Q1值, Q2值)
        """
        q1 = self.critic1(state, action)
        q2 = self.critic2(state, action)
        return q1, q2
    
    def q1(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取第一个Critic的Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: Q1值
        """
        return self.critic1(state, action)
    
    def q2(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取第二个Critic的Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: Q2值
        """
        return self.critic2(state, action)
    
    def min_q(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取两个Critic的最小Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 最小Q值
        """
        q1, q2 = self.forward(state, action)
        return torch.min(q1, q2)
    
    def get_parameters_count(self) -> int:
        """获取网络参数数量"""
        return (self.critic1.get_parameters_count() + 
                self.critic2.get_parameters_count())
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存网络检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'critic1_state_dict': self.critic1.state_dict(),
            'critic2_state_dict': self.critic2.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载网络检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location='cpu')
        self.critic1.load_state_dict(checkpoint['critic1_state_dict'])
        self.critic2.load_state_dict(checkpoint['critic2_state_dict'])
    
    def __repr__(self) -> str:
        """返回网络的字符串表示"""
        param_count = self.get_parameters_count()
        return (f"DoubleCritic(state_dim={self.state_dim}, action_dim={self.action_dim}, "
                f"parameters={param_count})")


class EnsembleCritic(nn.Module):
    """
    集成Critic网络
    
    包含多个独立的Critic网络，用于Multi-Critic SAC中的专家组。
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        n_critics: int = 2,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        output_activation: Optional[str] = None
    ):
        """
        初始化集成Critic网络
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            n_critics: Critic网络数量
            hidden_sizes: 隐藏层大小列表
            activation: 激活函数名称
            output_activation: 输出激活函数名称（可选）
        """
        super(EnsembleCritic, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.n_critics = n_critics
        
        # 创建多个独立的Critic网络
        self.critics = nn.ModuleList([
            Critic(state_dim, action_dim, hidden_sizes, activation, output_activation)
            for _ in range(n_critics)
        ])
        
        print(f"集成Critic网络初始化完成: 状态维度={state_dim}, 动作维度={action_dim}, "
              f"Critic数量={n_critics}")
    
    def forward(self, state: torch.Tensor, action: torch.Tensor) -> List[torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            List[torch.Tensor]: 所有Critic的Q值列表
        """
        q_values = []
        for critic in self.critics:
            q_values.append(critic(state, action))
        return q_values
    
    def mean_q(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取所有Critic的平均Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 平均Q值
        """
        q_values = self.forward(state, action)
        return torch.mean(torch.stack(q_values), dim=0)
    
    def min_q(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取所有Critic的最小Q值
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 最小Q值
        """
        q_values = self.forward(state, action)
        return torch.min(torch.stack(q_values), dim=0)[0]
    
    def get_parameters_count(self) -> int:
        """获取网络参数数量"""
        return sum(critic.get_parameters_count() for critic in self.critics)
    
    def __repr__(self) -> str:
        """返回网络的字符串表示"""
        param_count = self.get_parameters_count()
        return (f"EnsembleCritic(state_dim={self.state_dim}, action_dim={self.action_dim}, "
                f"n_critics={self.n_critics}, parameters={param_count})")
