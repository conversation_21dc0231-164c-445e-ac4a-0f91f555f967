"""
Multi-Critic SAC (多评论家软演员-评论家算法)

基于Stable-Baselines3的多评论家Soft Actor-Critic强化学习算法实现。
该框架通过引入多个专门的评论家网络来处理复杂的多目标任务。

主要组件:
- MultiCriticSAC: 主要算法类
- CriticExperts: 评论家专家组
- NavigationEnv: 导航环境
- Config: 配置管理

作者: Multi-Critic SAC Team
版本: 1.0.0
"""

from multi_critic_sac.algorithms.multi_critic_sac import MultiCriticSAC
from multi_critic_sac.config.config_manager import Config

__version__ = "1.0.0"
__author__ = "Multi-Critic SAC Team"

__all__ = [
    "MultiCriticSAC",
    "Config",
]
