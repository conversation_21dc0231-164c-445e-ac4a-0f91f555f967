"""
可视化工具模块

提供训练过程和结果的可视化功能，包括：
- 训练曲线绘制
- 评论家权重分析
- 环境状态可视化
- 性能对比图表

作者: Multi-Critic SAC Team
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
import pandas as pd
from pathlib import Path
import json


class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self, style: str = "seaborn-v0_8", figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            style: 绘图风格
            figsize: 图形大小
        """
        plt.style.use(style)
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    
    def plot_training_curves(self, log_data: Dict[str, List[float]], 
                           save_path: Optional[str] = None) -> None:
        """
        绘制训练曲线
        
        Args:
            log_data: 日志数据字典
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 奖励曲线
        if 'total_reward' in log_data:
            axes[0, 0].plot(log_data['total_reward'], color=self.colors[0], alpha=0.7)
            axes[0, 0].set_title('总奖励')
            axes[0, 0].set_xlabel('训练步数')
            axes[0, 0].set_ylabel('奖励')
            axes[0, 0].grid(True, alpha=0.3)
        
        # 损失曲线
        loss_keys = ['actor_loss', 'obstacle_total_loss', 'guidance_total_loss', 'environment_total_loss']
        for i, key in enumerate(loss_keys):
            if key in log_data:
                axes[0, 1].plot(log_data[key], label=key.replace('_', ' ').title(), 
                               color=self.colors[i % len(self.colors)], alpha=0.7)
        
        axes[0, 1].set_title('损失函数')
        axes[0, 1].set_xlabel('训练步数')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 分解奖励
        reward_keys = ['obstacle_reward', 'guidance_reward', 'environment_reward']
        for i, key in enumerate(reward_keys):
            if key in log_data:
                axes[1, 0].plot(log_data[key], label=key.replace('_', ' ').title(),
                               color=self.colors[i], alpha=0.7)
        
        axes[1, 0].set_title('分解奖励')
        axes[1, 0].set_xlabel('训练步数')
        axes[1, 0].set_ylabel('奖励')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # Q值
        q_keys = ['obstacle_mean_q1', 'guidance_mean_q1', 'environment_mean_q1']
        for i, key in enumerate(q_keys):
            if key in log_data:
                axes[1, 1].plot(log_data[key], label=key.replace('_', ' ').title(),
                               color=self.colors[i], alpha=0.7)
        
        axes[1, 1].set_title('Q值')
        axes[1, 1].set_xlabel('训练步数')
        axes[1, 1].set_ylabel('Q值')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练曲线已保存: {save_path}")
        
        plt.show()
    
    def plot_critic_weights_evolution(self, weights_data: Dict[str, List[float]], 
                                    save_path: Optional[str] = None) -> None:
        """
        绘制评论家权重演化
        
        Args:
            weights_data: 权重数据
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figsize)
        
        # 权重演化曲线
        weight_keys = ['obstacle_weight', 'guidance_weight', 'environment_weight']
        labels = ['避障专家', '引导专家', '环境专家']
        
        for i, (key, label) in enumerate(zip(weight_keys, labels)):
            if key in weights_data:
                ax1.plot(weights_data[key], label=label, color=self.colors[i], alpha=0.8)
        
        ax1.set_title('评论家权重演化')
        ax1.set_xlabel('训练步数')
        ax1.set_ylabel('权重')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)
        
        # 权重分布饼图（最终权重）
        if all(key in weights_data for key in weight_keys):
            final_weights = [weights_data[key][-1] for key in weight_keys]
            ax2.pie(final_weights, labels=labels, autopct='%1.1f%%', 
                   colors=self.colors[:3], startangle=90)
            ax2.set_title('最终权重分布')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"权重演化图已保存: {save_path}")
        
        plt.show()
    
    def plot_performance_comparison(self, results_dict: Dict[str, Dict[str, Any]], 
                                  save_path: Optional[str] = None) -> None:
        """
        绘制性能对比图
        
        Args:
            results_dict: 结果字典，键为模型名称
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 准备数据
        model_names = list(results_dict.keys())
        metrics = ['mean_reward', 'success_rate', 'mean_length', 'collision_rate']
        metric_labels = ['平均奖励', '成功率', '平均长度', '碰撞率']
        
        # 柱状图对比
        for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
            ax = axes[i // 2, i % 2]
            values = [results_dict[name].get(metric, 0) for name in model_names]
            
            bars = ax.bar(model_names, values, color=self.colors[:len(model_names)], alpha=0.7)
            ax.set_title(label)
            ax.set_ylabel(label)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}', ha='center', va='bottom')
            
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"性能对比图已保存: {save_path}")
        
        plt.show()
    
    def plot_reward_decomposition(self, episode_data: Dict[str, List[float]], 
                                save_path: Optional[str] = None) -> None:
        """
        绘制奖励分解分析
        
        Args:
            episode_data: 回合数据
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 分解奖励时间序列
        reward_keys = ['obstacle_rewards', 'guidance_rewards', 'environment_rewards']
        labels = ['避障奖励', '引导奖励', '环境奖励']
        
        for i, (key, label) in enumerate(zip(reward_keys, labels)):
            if key in episode_data:
                axes[0, 0].plot(episode_data[key], label=label, 
                               color=self.colors[i], alpha=0.7)
        
        axes[0, 0].set_title('分解奖励时间序列')
        axes[0, 0].set_xlabel('回合')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 奖励相关性热图
        if all(key in episode_data for key in reward_keys):
            reward_df = pd.DataFrame({
                '避障': episode_data['obstacle_rewards'],
                '引导': episode_data['guidance_rewards'],
                '环境': episode_data['environment_rewards'],
                '总奖励': episode_data.get('episode_rewards', [])
            })
            
            corr_matrix = reward_df.corr()
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       ax=axes[0, 1], square=True)
            axes[0, 1].set_title('奖励相关性')
        
        # 奖励分布箱线图
        if all(key in episode_data for key in reward_keys):
            reward_data = [episode_data[key] for key in reward_keys]
            axes[1, 0].boxplot(reward_data, labels=labels)
            axes[1, 0].set_title('奖励分布')
            axes[1, 0].set_ylabel('奖励')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 累积奖励贡献
        if all(key in episode_data for key in reward_keys):
            cumulative_rewards = []
            for key in reward_keys:
                cumulative_rewards.append(np.cumsum(episode_data[key]))
            
            for i, (cum_reward, label) in enumerate(zip(cumulative_rewards, labels)):
                axes[1, 1].plot(cum_reward, label=label, color=self.colors[i], alpha=0.7)
            
            axes[1, 1].set_title('累积奖励贡献')
            axes[1, 1].set_xlabel('回合')
            axes[1, 1].set_ylabel('累积奖励')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"奖励分解图已保存: {save_path}")
        
        plt.show()


class EnvironmentVisualizer:
    """环境可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (10, 10)):
        """
        初始化环境可视化器
        
        Args:
            figsize: 图形大小
        """
        self.figsize = figsize
    
    def plot_trajectory(self, trajectory: List[Tuple[float, float]], 
                       obstacles: List[Tuple[float, float, float]],
                       goal_pos: Tuple[float, float],
                       map_size: float = 100.0,
                       save_path: Optional[str] = None) -> None:
        """
        绘制智能体轨迹
        
        Args:
            trajectory: 轨迹点列表 [(x, y), ...]
            obstacles: 障碍物列表 [(x, y, radius), ...]
            goal_pos: 目标位置
            map_size: 地图大小
            save_path: 保存路径
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 设置地图边界
        boundary = map_size / 2
        ax.set_xlim(-boundary, boundary)
        ax.set_ylim(-boundary, boundary)
        
        # 绘制障碍物
        for obs_x, obs_y, obs_radius in obstacles:
            circle = plt.Circle((obs_x, obs_y), obs_radius, color='red', alpha=0.7)
            ax.add_patch(circle)
        
        # 绘制目标点
        goal_circle = plt.Circle(goal_pos, 1.0, color='green', alpha=0.7)
        ax.add_patch(goal_circle)
        
        # 绘制轨迹
        if trajectory:
            x_coords, y_coords = zip(*trajectory)
            ax.plot(x_coords, y_coords, 'b-', linewidth=2, alpha=0.8, label='轨迹')
            
            # 标记起点和终点
            ax.plot(x_coords[0], y_coords[0], 'go', markersize=10, label='起点')
            ax.plot(x_coords[-1], y_coords[-1], 'ro', markersize=10, label='终点')
        
        ax.set_title('智能体导航轨迹')
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"轨迹图已保存: {save_path}")
        
        plt.show()
    
    def plot_state_analysis(self, state_history: List[np.ndarray], 
                           save_path: Optional[str] = None) -> None:
        """
        绘制状态分析图
        
        Args:
            state_history: 状态历史
            save_path: 保存路径
        """
        if not state_history:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 提取状态分量
        radar_data = [state[:36] for state in state_history]
        target_vectors = [state[36:38] for state in state_history]
        self_states = [state[38:40] for state in state_history]
        env_data = [state[40:44] for state in state_history]
        
        # 最小障碍物距离
        min_distances = [np.min(radar) for radar in radar_data]
        axes[0, 0].plot(min_distances, 'r-', alpha=0.7)
        axes[0, 0].set_title('最小障碍物距离')
        axes[0, 0].set_xlabel('时间步')
        axes[0, 0].set_ylabel('距离')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 到目标距离
        target_distances = [np.linalg.norm(target) for target in target_vectors]
        axes[0, 1].plot(target_distances, 'g-', alpha=0.7)
        axes[0, 1].set_title('到目标距离')
        axes[0, 1].set_xlabel('时间步')
        axes[0, 1].set_ylabel('距离')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 速度变化
        velocities = [self_state[0] for self_state in self_states]
        angular_velocities = [self_state[1] for self_state in self_states]
        axes[1, 0].plot(velocities, 'b-', alpha=0.7, label='线速度')
        axes[1, 0].plot(angular_velocities, 'orange', alpha=0.7, label='角速度')
        axes[1, 0].set_title('速度变化')
        axes[1, 0].set_xlabel('时间步')
        axes[1, 0].set_ylabel('速度')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 环境因素
        current_magnitudes = [np.linalg.norm(env[:2]) for env in env_data]
        wind_magnitudes = [np.linalg.norm(env[2:]) for env in env_data]
        axes[1, 1].plot(current_magnitudes, 'cyan', alpha=0.7, label='洋流强度')
        axes[1, 1].plot(wind_magnitudes, 'purple', alpha=0.7, label='风力强度')
        axes[1, 1].set_title('环境因素')
        axes[1, 1].set_xlabel('时间步')
        axes[1, 1].set_ylabel('强度')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"状态分析图已保存: {save_path}")
        
        plt.show()


def load_tensorboard_data(log_dir: str) -> Dict[str, List[float]]:
    """
    从TensorBoard日志加载数据
    
    Args:
        log_dir: 日志目录
        
    Returns:
        Dict[str, List[float]]: 日志数据
    """
    try:
        from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
        
        ea = EventAccumulator(log_dir)
        ea.Reload()
        
        data = {}
        for tag in ea.Tags()['scalars']:
            scalar_events = ea.Scalars(tag)
            data[tag] = [event.value for event in scalar_events]
        
        return data
    
    except ImportError:
        print("警告: 无法导入TensorBoard，请安装tensorboard包")
        return {}
    except Exception as e:
        print(f"加载TensorBoard数据时出错: {e}")
        return {}
