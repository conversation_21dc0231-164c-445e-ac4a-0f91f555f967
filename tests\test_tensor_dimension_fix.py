"""
测试张量维度修复

验证修复后的专家网络不再产生PyTorch张量维度不匹配警告，
并确保所有张量维度正确匹配。
"""

import sys
import os
import warnings

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from multi_critic_sac.critics.obstacle_expert import ObstacleExpert
from multi_critic_sac.critics.guidance_expert import <PERSON>uidanceExpert
from multi_critic_sac.critics.environment_expert import EnvironmentExpert
from multi_critic_sac.networks.actor import Actor


def test_tensor_dimension_fix():
    """测试张量维度修复"""
    print("🔧 开始测试张量维度修复...")
    
    # 捕获警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            device = torch.device("cpu")
            batch_size = 4
            state_dim = 45
            action_dim = 2
            
            # 创建网络
            actor = Actor(
                state_dim=state_dim,
                action_dim=action_dim,
                hidden_sizes=[256, 256],
                activation="relu"
            ).to(device)
            
            obstacle_expert = ObstacleExpert(
                state_dim=state_dim,
                action_dim=action_dim,
                device=device
            )
            
            guidance_expert = GuidanceExpert(
                state_dim=state_dim,
                action_dim=action_dim,
                device=device
            )
            
            environment_expert = EnvironmentExpert(
                state_dim=state_dim,
                action_dim=action_dim,
                device=device
            )
            
            print("测试1: 验证张量维度一致性")
            
            # 创建测试数据
            states = torch.randn(batch_size, state_dim, device=device)
            actions = torch.randn(batch_size, action_dim, device=device)
            next_states = torch.randn(batch_size, state_dim, device=device)
            dones = torch.zeros(batch_size, dtype=torch.bool, device=device)
            
            batch = {
                'states': states,
                'actions': actions,
                'rewards': torch.randn(batch_size),
                'next_states': next_states,
                'dones': dones,
                'obstacle_rewards': torch.randn(batch_size),      # [batch_size]
                'guidance_rewards': torch.randn(batch_size),      # [batch_size]
                'environment_rewards': torch.randn(batch_size)    # [batch_size]
            }
            
            print("📊 输入张量维度:")
            print(f"  states: {batch['states'].shape}")
            print(f"  actions: {batch['actions'].shape}")
            print(f"  obstacle_rewards: {batch['obstacle_rewards'].shape}")
            print(f"  guidance_rewards: {batch['guidance_rewards'].shape}")
            print(f"  environment_rewards: {batch['environment_rewards'].shape}")
            
            # 检查Q值输出维度
            q1, q2 = obstacle_expert.critic(states, actions)
            print(f"📊 Q值输出维度:")
            print(f"  q1: {q1.shape}")
            print(f"  q2: {q2.shape}")
            
            print("测试2: 专家网络更新（检查是否有警告）")
            
            # 记录更新前的警告数量
            warnings_before = len(w)
            
            # 测试避障专家更新
            obstacle_metrics = obstacle_expert.update(batch, gamma=0.99, actor=actor)
            warnings_after_obstacle = len(w)
            
            # 测试引导专家更新
            guidance_metrics = guidance_expert.update(batch, gamma=0.99, actor=actor)
            warnings_after_guidance = len(w)
            
            # 测试环境影响专家更新
            environment_metrics = environment_expert.update(batch, gamma=0.99, actor=actor)
            warnings_after_environment = len(w)
            
            print(f"📊 警告统计:")
            print(f"  更新前警告数: {warnings_before}")
            print(f"  避障专家更新后: {warnings_after_obstacle}")
            print(f"  引导专家更新后: {warnings_after_guidance}")
            print(f"  环境专家更新后: {warnings_after_environment}")
            
            # 检查是否有MSELoss相关的警告
            mse_warnings = [warning for warning in w if "mse_loss" in str(warning.message).lower() or "target size" in str(warning.message)]
            
            if mse_warnings:
                print(f"❌ 仍然存在MSELoss警告:")
                for warning in mse_warnings:
                    print(f"  - {warning.message}")
                return False
            else:
                print("✅ 没有MSELoss维度不匹配警告")
            
            print("测试3: 验证损失值的数值稳定性")
            
            # 检查损失值是否合理
            assert np.isfinite(obstacle_metrics['obstacle_total_loss'])
            assert np.isfinite(guidance_metrics['guidance_total_loss'])
            assert np.isfinite(environment_metrics['environment_total_loss'])
            
            print(f"📊 损失值:")
            print(f"  避障专家总损失: {obstacle_metrics['obstacle_total_loss']:.6f}")
            print(f"  引导专家总损失: {guidance_metrics['guidance_total_loss']:.6f}")
            print(f"  环境专家总损失: {environment_metrics['environment_total_loss']:.6f}")
            
            print("测试4: 多批次一致性测试")
            
            # 测试多个不同大小的批次
            for test_batch_size in [2, 8, 16]:
                test_batch = {
                    'states': torch.randn(test_batch_size, state_dim, device=device),
                    'actions': torch.randn(test_batch_size, action_dim, device=device),
                    'rewards': torch.randn(test_batch_size),
                    'next_states': torch.randn(test_batch_size, state_dim, device=device),
                    'dones': torch.zeros(test_batch_size, dtype=torch.bool, device=device),
                    'obstacle_rewards': torch.randn(test_batch_size),
                    'guidance_rewards': torch.randn(test_batch_size),
                    'environment_rewards': torch.randn(test_batch_size)
                }
                
                warnings_before_batch = len(w)
                
                # 测试所有专家
                obstacle_expert.update(test_batch, gamma=0.99, actor=actor)
                guidance_expert.update(test_batch, gamma=0.99, actor=actor)
                environment_expert.update(test_batch, gamma=0.99, actor=actor)
                
                warnings_after_batch = len(w)
                
                # 检查是否有新的MSELoss警告
                new_mse_warnings = [warning for warning in w[warnings_before_batch:] 
                                  if "mse_loss" in str(warning.message).lower() or "target size" in str(warning.message)]
                
                if new_mse_warnings:
                    print(f"❌ 批次大小{test_batch_size}产生了MSELoss警告")
                    return False
                
            print("✅ 所有批次大小都没有产生警告")
            
            print("测试5: 验证张量形状的正确性")
            
            # 手动检查内部张量形状
            with torch.no_grad():
                # 模拟避障专家的内部计算
                q1, q2 = obstacle_expert.critic(states, actions)
                next_actions, _ = actor.sample(next_states)
                target_q_raw = obstacle_expert.target_critic.min_q(next_states, next_actions)
                
                # 检查reshape后的张量
                obstacle_rewards_reshaped = batch['obstacle_rewards'].unsqueeze(-1)
                dones_reshaped = dones.float().unsqueeze(-1)
                
                print(f"📊 内部张量维度验证:")
                print(f"  q1: {q1.shape}")
                print(f"  target_q_raw: {target_q_raw.shape}")
                print(f"  obstacle_rewards_reshaped: {obstacle_rewards_reshaped.shape}")
                print(f"  dones_reshaped: {dones_reshaped.shape}")
                
                # 计算最终目标Q值
                target_q_final = obstacle_rewards_reshaped + 0.99 * (1 - dones_reshaped) * target_q_raw
                print(f"  target_q_final: {target_q_final.shape}")
                
                # 验证维度匹配
                assert q1.shape == target_q_final.shape, f"Q值和目标Q值维度不匹配: {q1.shape} vs {target_q_final.shape}"
                assert q2.shape == target_q_final.shape, f"Q值和目标Q值维度不匹配: {q2.shape} vs {target_q_final.shape}"
                
            print("✅ 所有张量维度正确匹配")
            
            print("\n🎉 所有测试通过！张量维度修复成功。")
            
            # 打印修复总结
            print("\n📋 修复总结:")
            print("1. ✅ 消除了MSELoss张量维度不匹配警告")
            print("2. ✅ 所有专家网络的张量维度正确匹配")
            print("3. ✅ 损失计算数值稳定")
            print("4. ✅ 支持不同批次大小")
            print("5. ✅ Q值和目标Q值维度一致: [batch_size, 1]")
            
            print("\n🔧 修复的关键变化:")
            print("- 奖励张量: .unsqueeze(-1) 从 [batch_size] 到 [batch_size, 1]")
            print("- 终止标志: .unsqueeze(-1) 从 [batch_size] 到 [batch_size, 1]")
            print("- 避免了PyTorch的自动广播导致的维度错误")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = test_tensor_dimension_fix()
    sys.exit(0 if success else 1)
