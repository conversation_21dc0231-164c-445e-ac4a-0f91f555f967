"""
日志记录模块

提供训练过程中的日志记录和监控功能，支持TensorBoard集成。

作者: Multi-Critic SAC Team
"""

import os
import numpy as np
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import logging
from datetime import datetime

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    print("警告: TensorBoard不可用，将使用基础日志功能")

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False


class Logger:
    """
    多功能日志记录器
    
    支持控制台输出、文件记录、TensorBoard和Weights & Biases集成。
    """
    
    def __init__(
        self,
        log_dir: str = "./logs/",
        experiment_name: Optional[str] = None,
        use_tensorboard: bool = True,
        use_wandb: bool = False,
        wandb_project: Optional[str] = None,
        log_level: int = logging.INFO
    ):
        """
        初始化日志记录器
        
        Args:
            log_dir: 日志目录
            experiment_name: 实验名称
            use_tensorboard: 是否使用TensorBoard
            use_wandb: 是否使用Weights & Biases
            wandb_project: W&B项目名称
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.experiment_name = experiment_name or f"experiment_{self._get_timestamp()}"
        
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置基础日志记录器
        self._setup_basic_logger(log_level)
        
        # 初始化TensorBoard
        self.tensorboard_writer = None
        if use_tensorboard and TENSORBOARD_AVAILABLE:
            self._setup_tensorboard()
        
        # 初始化Weights & Biases
        self.use_wandb = use_wandb and WANDB_AVAILABLE
        if self.use_wandb:
            self._setup_wandb(wandb_project)
        
        # 存储指标历史
        self.metrics_history: Dict[str, List[float]] = {}
        self.step_count = 0
        
        self.logger.info(f"日志记录器初始化完成: {self.experiment_name}")
    
    def _get_timestamp(self) -> str:
        """获取时间戳字符串"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _setup_basic_logger(self, log_level: int) -> None:
        """设置基础日志记录器"""
        self.logger = logging.getLogger(f"MultiCriticSAC_{self.experiment_name}")
        self.logger.setLevel(log_level)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self.log_dir / f"{self.experiment_name}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def _setup_tensorboard(self) -> None:
        """设置TensorBoard"""
        try:
            tensorboard_dir = self.log_dir / "tensorboard" / self.experiment_name
            self.tensorboard_writer = SummaryWriter(log_dir=str(tensorboard_dir))
            self.logger.info(f"TensorBoard日志目录: {tensorboard_dir}")
        except Exception as e:
            self.logger.warning(f"TensorBoard初始化失败: {e}")
            self.tensorboard_writer = None
    
    def _setup_wandb(self, project_name: Optional[str]) -> None:
        """设置Weights & Biases"""
        try:
            wandb.init(
                project=project_name or "multi-critic-sac",
                name=self.experiment_name,
                dir=str(self.log_dir)
            )
            self.logger.info("Weights & Biases初始化成功")
        except Exception as e:
            self.logger.warning(f"Weights & Biases初始化失败: {e}")
            self.use_wandb = False
    
    def log_scalar(self, tag: str, value: float, step: Optional[int] = None) -> None:
        """
        记录标量值
        
        Args:
            tag: 标签名称
            value: 数值
            step: 步数（可选）
        """
        if step is None:
            step = self.step_count
        
        # 更新指标历史
        if tag not in self.metrics_history:
            self.metrics_history[tag] = []
        self.metrics_history[tag].append(value)
        
        # TensorBoard记录
        if self.tensorboard_writer:
            self.tensorboard_writer.add_scalar(tag, value, step)
        
        # Weights & Biases记录
        if self.use_wandb:
            wandb.log({tag: value}, step=step)
    
    def log_scalars(self, tag_value_dict: Dict[str, float], 
                   step: Optional[int] = None) -> None:
        """
        批量记录标量值
        
        Args:
            tag_value_dict: 标签-数值字典
            step: 步数（可选）
        """
        if step is None:
            step = self.step_count
        
        for tag, value in tag_value_dict.items():
            self.log_scalar(tag, value, step)
    
    def log_histogram(self, tag: str, values: np.ndarray, 
                     step: Optional[int] = None) -> None:
        """
        记录直方图
        
        Args:
            tag: 标签名称
            values: 数值数组
            step: 步数（可选）
        """
        if step is None:
            step = self.step_count
        
        if self.tensorboard_writer:
            self.tensorboard_writer.add_histogram(tag, values, step)
        
        if self.use_wandb:
            wandb.log({f"{tag}_histogram": wandb.Histogram(values)}, step=step)
    
    def log_text(self, tag: str, text: str, step: Optional[int] = None) -> None:
        """
        记录文本
        
        Args:
            tag: 标签名称
            text: 文本内容
            step: 步数（可选）
        """
        if step is None:
            step = self.step_count
        
        if self.tensorboard_writer:
            self.tensorboard_writer.add_text(tag, text, step)
        
        if self.use_wandb:
            wandb.log({tag: text}, step=step)
    
    def log_episode_metrics(self, episode: int, metrics: Dict[str, float]) -> None:
        """
        记录回合指标
        
        Args:
            episode: 回合数
            metrics: 指标字典
        """
        # 记录到日志文件
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(f"Episode {episode}: {metrics_str}")
        
        # 记录到TensorBoard和W&B
        for metric_name, value in metrics.items():
            self.log_scalar(f"episode/{metric_name}", value, episode)
    
    def log_training_metrics(self, step: int, metrics: Dict[str, float]) -> None:
        """
        记录训练指标
        
        Args:
            step: 训练步数
            metrics: 指标字典
        """
        for metric_name, value in metrics.items():
            self.log_scalar(f"training/{metric_name}", value, step)
    
    def log_evaluation_metrics(self, step: int, metrics: Dict[str, float]) -> None:
        """
        记录评估指标
        
        Args:
            step: 训练步数
            metrics: 指标字典
        """
        for metric_name, value in metrics.items():
            self.log_scalar(f"evaluation/{metric_name}", value, step)
    
    def log_critic_metrics(self, step: int, critic_name: str, 
                          metrics: Dict[str, float]) -> None:
        """
        记录评论家指标
        
        Args:
            step: 训练步数
            critic_name: 评论家名称
            metrics: 指标字典
        """
        for metric_name, value in metrics.items():
            self.log_scalar(f"critics/{critic_name}/{metric_name}", value, step)
    
    def get_metric_history(self, metric_name: str) -> List[float]:
        """
        获取指标历史
        
        Args:
            metric_name: 指标名称
            
        Returns:
            List[float]: 指标历史列表
        """
        return self.metrics_history.get(metric_name, [])
    
    def get_latest_metric(self, metric_name: str) -> Optional[float]:
        """
        获取最新指标值
        
        Args:
            metric_name: 指标名称
            
        Returns:
            Optional[float]: 最新指标值
        """
        history = self.get_metric_history(metric_name)
        return history[-1] if history else None
    
    def save_metrics_summary(self, file_path: Optional[str] = None) -> None:
        """
        保存指标摘要
        
        Args:
            file_path: 保存路径（可选）
        """
        if file_path is None:
            file_path = self.log_dir / f"{self.experiment_name}_metrics_summary.json"
        
        summary = {}
        for metric_name, history in self.metrics_history.items():
            if history:
                summary[metric_name] = {
                    "mean": float(np.mean(history)),
                    "std": float(np.std(history)),
                    "min": float(np.min(history)),
                    "max": float(np.max(history)),
                    "latest": float(history[-1]),
                    "count": len(history)
                }
        
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"指标摘要已保存到: {file_path}")
    
    def close(self) -> None:
        """关闭日志记录器"""
        if self.tensorboard_writer:
            self.tensorboard_writer.close()
        
        if self.use_wandb:
            wandb.finish()
        
        self.logger.info("日志记录器已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
