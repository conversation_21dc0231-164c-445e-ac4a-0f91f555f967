#!/usr/bin/env python3
"""
Multi-Critic SAC基础使用示例

演示如何使用Multi-Critic SAC算法进行基础的训练和评估。
包括环境创建、模型配置、训练过程和结果分析。

作者: Multi-Critic SAC Team
"""

import sys
from pathlib import Path
import numpy as np
import torch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from multi_critic_sac.config import get_default_config
from multi_critic_sac.utils.common import set_seed, create_directories
from multi_critic_sac.utils.visualization import TrainingVisualizer


def basic_training_example():
    """基础训练示例"""
    print("="*60)
    print("Multi-Critic SAC 基础训练示例")
    print("="*60)
    
    # 设置随机种子
    set_seed(42)
    
    # 创建环境
    env = NavigationEnv(
        map_size=50.0,
        num_obstacles=5,
        max_episode_steps=200,
        seed=42
    )
    
    print(f"环境信息:")
    print(f"  观测空间: {env.observation_space}")
    print(f"  动作空间: {env.action_space}")
    
    # 获取配置
    config = get_default_config()
    
    # 创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        learning_rate=config.algorithm.learning_rate,
        buffer_size=50000,  # 减小缓冲区以加快示例运行
        learning_starts=500,
        batch_size=128,
        tau=config.algorithm.tau,
        gamma=config.algorithm.gamma,
        critic_weights=config.critic_weights.__dict__,
        verbose=1,
        seed=42
    )
    
    print("\n开始训练...")
    
    # 训练模型
    model.learn(
        total_timesteps=5000,  # 较少的步数用于示例
        log_interval=1000
    )
    
    print("训练完成!")
    
    # 保存模型
    create_directories(["models"])
    model.save("models/basic_example_model.pt")
    print("模型已保存到: models/basic_example_model.pt")
    
    # 评估模型
    print("\n开始评估...")
    evaluate_model(model, env, n_episodes=3)
    
    env.close()
    return model


def evaluate_model(model, env, n_episodes=5):
    """评估模型性能"""
    episode_rewards = []
    episode_lengths = []
    success_count = 0
    
    for episode in range(n_episodes):
        obs, _ = env.reset()
        episode_reward = 0
        episode_length = 0
        
        done = False
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            episode_length += 1
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(episode_length)
        
        # 检查是否成功到达目标
        env_info = env.get_environment_info()
        if env_info['distance_to_goal'] < env.goal_threshold:
            success_count += 1
        
        print(f"回合 {episode+1}: 奖励={episode_reward:.2f}, "
              f"长度={episode_length}, 到目标距离={env_info['distance_to_goal']:.2f}")
    
    # 计算统计信息
    mean_reward = np.mean(episode_rewards)
    std_reward = np.std(episode_rewards)
    success_rate = success_count / n_episodes
    
    print(f"\n评估结果:")
    print(f"  平均奖励: {mean_reward:.2f} ± {std_reward:.2f}")
    print(f"  成功率: {success_rate:.1%}")
    print(f"  平均回合长度: {np.mean(episode_lengths):.1f}")


def custom_config_example():
    """自定义配置示例"""
    print("\n" + "="*60)
    print("自定义配置示例")
    print("="*60)
    
    # 获取默认配置
    config = get_default_config()
    
    # 修改配置
    config.algorithm.learning_rate = 1e-3
    config.algorithm.batch_size = 64
    config.critic_weights.obstacle = 0.5
    config.critic_weights.guidance = 0.3
    config.critic_weights.environment = 0.2
    
    print("自定义配置:")
    print(f"  学习率: {config.algorithm.learning_rate}")
    print(f"  批次大小: {config.algorithm.batch_size}")
    print(f"  评论家权重: {config.critic_weights.__dict__}")
    
    # 创建环境
    env = NavigationEnv(
        map_size=30.0,
        num_obstacles=3,
        max_episode_steps=150
    )
    
    # 使用自定义配置创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        learning_rate=config.algorithm.learning_rate,
        batch_size=config.algorithm.batch_size,
        critic_weights=config.critic_weights.__dict__,
        verbose=1
    )
    
    print("使用自定义配置的模型创建成功!")
    
    # 简短训练
    model.learn(total_timesteps=2000, log_interval=500)
    
    # 评估
    evaluate_model(model, env, n_episodes=2)
    
    env.close()


def reward_analysis_example():
    """奖励分析示例"""
    print("\n" + "="*60)
    print("奖励分析示例")
    print("="*60)
    
    # 创建环境
    env = NavigationEnv(
        map_size=40.0,
        num_obstacles=4,
        max_episode_steps=100
    )
    
    # 创建简单模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        learning_starts=100,
        verbose=0
    )
    
    # 收集一个回合的详细数据
    obs, _ = env.reset()
    
    rewards_data = {
        'total_rewards': [],
        'obstacle_rewards': [],
        'guidance_rewards': [],
        'environment_rewards': [],
        'states': [],
        'actions': []
    }
    
    print("收集回合数据...")
    done = False
    step = 0
    
    while not done and step < 50:  # 限制步数
        action, _ = model.predict(obs, deterministic=False)
        new_obs, reward, terminated, truncated, info = env.step(action)
        done = terminated or truncated
        
        # 记录数据
        rewards_data['total_rewards'].append(reward)
        rewards_data['obstacle_rewards'].append(info.get('obstacle_reward', 0))
        rewards_data['guidance_rewards'].append(info.get('guidance_reward', 0))
        rewards_data['environment_rewards'].append(info.get('environment_reward', 0))
        rewards_data['states'].append(obs.copy())
        rewards_data['actions'].append(action.copy())
        
        obs = new_obs
        step += 1
    
    # 分析奖励组成
    total_reward = sum(rewards_data['total_rewards'])
    obstacle_total = sum(rewards_data['obstacle_rewards'])
    guidance_total = sum(rewards_data['guidance_rewards'])
    environment_total = sum(rewards_data['environment_rewards'])
    
    print(f"\n奖励分析结果 ({step}步):")
    print(f"  总奖励: {total_reward:.2f}")
    print(f"  避障奖励: {obstacle_total:.2f} ({obstacle_total/total_reward:.1%})")
    print(f"  引导奖励: {guidance_total:.2f} ({guidance_total/total_reward:.1%})")
    print(f"  环境奖励: {environment_total:.2f} ({environment_total/total_reward:.1%})")
    
    # 分析状态变化
    initial_state = rewards_data['states'][0]
    final_state = rewards_data['states'][-1]
    
    initial_target_dist = np.linalg.norm(initial_state[36:38])
    final_target_dist = np.linalg.norm(final_state[36:38])
    
    print(f"\n状态变化:")
    print(f"  初始到目标距离: {initial_target_dist:.2f}")
    print(f"  最终到目标距离: {final_target_dist:.2f}")
    print(f"  距离改善: {initial_target_dist - final_target_dist:.2f}")
    
    env.close()


def model_comparison_example():
    """模型对比示例"""
    print("\n" + "="*60)
    print("模型对比示例")
    print("="*60)
    
    # 创建环境
    env = NavigationEnv(
        map_size=35.0,
        num_obstacles=3,
        max_episode_steps=120
    )
    
    # 配置1：平衡权重
    config1 = get_default_config()
    model1 = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        critic_weights=config1.critic_weights.__dict__,
        learning_starts=200,
        verbose=0
    )
    
    # 配置2：重视避障
    config2 = get_default_config()
    config2.critic_weights.obstacle = 0.6
    config2.critic_weights.guidance = 0.3
    config2.critic_weights.environment = 0.1
    
    model2 = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        critic_weights=config2.critic_weights.__dict__,
        learning_starts=200,
        verbose=0
    )
    
    print("训练两个不同配置的模型...")
    
    # 训练模型
    model1.learn(total_timesteps=3000)
    model2.learn(total_timesteps=3000)
    
    # 对比评估
    print("\n模型对比评估:")
    
    models = [model1, model2]
    configs = [config1.critic_weights.__dict__, config2.critic_weights.__dict__]
    names = ["平衡权重", "重视避障"]
    
    for i, (model, config, name) in enumerate(zip(models, configs, names)):
        print(f"\n{name}模型 (权重: {config}):")
        evaluate_model(model, env, n_episodes=3)
    
    env.close()


def main():
    """主函数"""
    print("Multi-Critic SAC 使用示例集合")
    print("作者: Multi-Critic SAC Team")
    
    try:
        # 1. 基础训练示例
        basic_training_example()
        
        # 2. 自定义配置示例
        custom_config_example()
        
        # 3. 奖励分析示例
        reward_analysis_example()
        
        # 4. 模型对比示例
        model_comparison_example()
        
        print("\n" + "="*60)
        print("所有示例运行完成!")
        print("="*60)
        
        print("\n更多功能:")
        print("- 使用 scripts/train.py 进行完整训练")
        print("- 使用 scripts/evaluate.py 进行详细评估")
        print("- 使用 scripts/monitor.py 监控训练过程")
        print("- 查看 configs/default.yaml 了解配置选项")
        
    except KeyboardInterrupt:
        print("\n示例被用户中断")
    except Exception as e:
        print(f"\n示例运行中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
