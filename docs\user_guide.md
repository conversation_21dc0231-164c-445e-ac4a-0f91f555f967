# Multi-Critic SAC 用户指南

## 目录

1. [快速开始](#快速开始)
2. [核心概念](#核心概念)
3. [配置系统](#配置系统)
4. [训练模型](#训练模型)
5. [评估模型](#评估模型)
6. [高级功能](#高级功能)
7. [故障排除](#故障排除)

## 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/multi-critic-sac/multi-critic-sac.git
cd multi-critic-sac

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install -e .
```

### 运行演示

```bash
# 运行基础演示
python scripts/demo.py

# 运行基础使用示例
python examples/basic_usage.py
```

### 快速训练

```bash
# 使用默认配置训练
python scripts/train.py --config configs/default.yaml

# 监控训练过程
python scripts/monitor.py --log-dir logs/tensorboard/experiment_name
```

## 核心概念

### Multi-Critic SAC架构

Multi-Critic SAC是基于SAC算法的扩展，通过引入多个专门的评论家网络来处理多目标优化问题：

- **避障专家（Obstacle Expert）**: 负责评估避障行为，惩罚碰撞风险
- **引导专家（Guidance Expert）**: 负责评估目标导向行为，奖励朝向目标的移动
- **环境影响专家（Environment Expert）**: 负责评估环境因素影响，优化能量效率

### 状态空间（44维）

```
状态向量组成：
├── 雷达数据 (36维): 360°障碍物距离探测，每10°一个数据点
├── 目标指向 (2维): 到目标的距离和角度
├── 自身状态 (2维): 当前速度和角速度
└── 环境数据 (4维): 洋流向量(x,y)和风力向量(x,y)
```

### 动作空间（2维）

```
动作向量：[线速度, 角速度]
取值范围：[-1, 1]
实际映射：
├── 线速度: [-1, 1] → [-max_linear_velocity, max_linear_velocity]
└── 角速度: [-1, 1] → [-max_angular_velocity, max_angular_velocity]
```

## 配置系统

### 配置文件结构

```yaml
# configs/default.yaml
algorithm:
  learning_rate: 3.0e-4
  batch_size: 256
  # ... 其他算法参数

critic_weights:
  obstacle: 0.4      # 避障专家权重
  guidance: 0.4      # 引导专家权重
  environment: 0.2   # 环境影响专家权重

network:
  actor:
    hidden_sizes: [256, 256]
    activation: "relu"
  critic:
    hidden_sizes: [256, 256]
    n_critics: 2

environment:
  max_episode_steps: 1000
  state_space:
    radar_points: 36
    # ... 其他环境参数

training:
  total_timesteps: 1000000
  n_envs: 4
  vec_env_type: "dummy"

rewards:
  obstacle:
    collision_penalty: -50.0
    safety_threshold: 2.0
  guidance:
    goal_reward: 100.0
    progress_scale: 1.0
  environment:
    current_scale: 0.5
    wind_scale: 0.3
```

### 使用配置

```python
from multi_critic_sac.config import load_config, get_default_config

# 加载配置文件
config = load_config("configs/default.yaml")

# 获取默认配置
config = get_default_config()

# 修改配置
config.algorithm.learning_rate = 1e-3
config.critic_weights.obstacle = 0.5
```

## 训练模型

### 基础训练

```python
from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv

# 创建环境
env = NavigationEnv()

# 创建模型
model = MultiCriticSAC(
    policy="MlpPolicy",
    env=env,
    learning_rate=3e-4,
    critic_weights={'obstacle': 0.4, 'guidance': 0.4, 'environment': 0.2},
    verbose=1
)

# 训练
model.learn(total_timesteps=100000)

# 保存模型
model.save("my_model.pt")
```

### 使用脚本训练

```bash
# 基础训练
python scripts/train.py --config configs/default.yaml

# 指定输出目录和实验名称
python scripts/train.py \
    --config configs/default.yaml \
    --output-dir experiments \
    --experiment-name my_experiment

# 从检查点恢复训练
python scripts/train.py \
    --config configs/default.yaml \
    --resume models/checkpoint_50000_steps.pt
```

### 多进程训练

```python
from stable_baselines3.common.vec_env import SubprocVecEnv
from stable_baselines3.common.monitor import Monitor

def make_env(rank, seed=0):
    def _init():
        env = NavigationEnv(seed=seed + rank)
        env = Monitor(env)
        return env
    return _init

# 创建多进程环境
n_envs = 4
env_fns = [make_env(i) for i in range(n_envs)]
vec_env = SubprocVecEnv(env_fns)

# 使用多进程环境训练
model = MultiCriticSAC("MlpPolicy", vec_env)
model.learn(total_timesteps=100000)
```

## 评估模型

### 基础评估

```python
# 加载模型
model = MultiCriticSAC.load("my_model.pt")

# 创建评估环境
env = NavigationEnv()

# 评估
obs, _ = env.reset()
for _ in range(1000):
    action, _ = model.predict(obs, deterministic=True)
    obs, reward, terminated, truncated, info = env.step(action)
    if terminated or truncated:
        obs, _ = env.reset()
```

### 使用脚本评估

```bash
# 基础评估
python scripts/evaluate.py --model models/my_model.pt --episodes 10

# 带可视化的评估
python scripts/evaluate.py \
    --model models/my_model.pt \
    --episodes 5 \
    --render

# 指定输出目录
python scripts/evaluate.py \
    --model models/my_model.pt \
    --episodes 10 \
    --output-dir evaluation_results
```

### 性能分析

```python
from scripts.evaluate import ModelEvaluator

# 创建评估器
evaluator = ModelEvaluator("models/my_model.pt")

# 评估多个回合
results = evaluator.evaluate_episodes(n_episodes=20)

# 分析性能
evaluator.analyze_performance(results)

# 绘制结果
evaluator.plot_results(results, "results.png")

# 保存结果
evaluator.save_results(results, "results.json")
```

## 高级功能

### 自定义环境

```python
from multi_critic_sac.envs import BaseEnv

class MyCustomEnv(BaseEnv):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 自定义初始化
    
    def _get_observation(self):
        # 返回44维观测向量
        return observation
    
    def _calculate_reward(self, action):
        # 返回奖励字典
        return {
            'total_reward': total_reward,
            'obstacle_reward': obstacle_reward,
            'guidance_reward': guidance_reward,
            'environment_reward': environment_reward
        }
    
    def _is_terminated(self):
        # 检查终止条件
        return terminated
    
    def _execute_action(self, action):
        # 执行动作
        pass
```

### 自定义评论家权重

```python
# 静态权重
model = MultiCriticSAC(
    policy="MlpPolicy",
    env=env,
    critic_weights={'obstacle': 0.6, 'guidance': 0.3, 'environment': 0.1}
)

# 动态权重（自适应）
from multi_critic_sac.critics.fusion_module import FusionModule

fusion_module = FusionModule(
    obstacle_expert=obstacle_expert,
    guidance_expert=guidance_expert,
    environment_expert=environment_expert,
    fusion_strategy="weighted_sum",
    adaptive_weights=True
)
```

### 课程学习

```python
# 阶段1：简单环境
easy_env = NavigationEnv(map_size=30, num_obstacles=2)
model = MultiCriticSAC("MlpPolicy", easy_env)
model.learn(total_timesteps=50000)

# 阶段2：中等环境
normal_env = NavigationEnv(map_size=50, num_obstacles=5)
model.set_env(normal_env)
model.learn(total_timesteps=100000)

# 阶段3：困难环境
hard_env = NavigationEnv(map_size=80, num_obstacles=10)
model.set_env(hard_env)
model.learn(total_timesteps=150000)
```

### 迁移学习

```python
# 训练源模型
source_env = NavigationEnv(map_size=30)
source_model = MultiCriticSAC("MlpPolicy", source_env)
source_model.learn(total_timesteps=100000)
source_model.save("source_model.pt")

# 在目标任务上微调
target_env = NavigationEnv(map_size=80)
target_model = MultiCriticSAC("MlpPolicy", target_env)
target_model.load("source_model.pt")  # 加载预训练权重
target_model.learn(total_timesteps=50000)  # 微调
```

## 故障排除

### 常见问题

**Q: 训练过程中奖励不收敛**
A: 
- 检查评论家权重是否合理
- 调整学习率和批次大小
- 确保环境奖励设计合理
- 增加训练步数

**Q: 模型在评估时性能差**
A:
- 使用确定性策略评估 (`deterministic=True`)
- 检查环境随机性设置
- 确保模型完全训练
- 验证状态归一化

**Q: 内存不足错误**
A:
- 减小经验回放缓冲区大小
- 减小批次大小
- 使用CPU训练
- 减少并行环境数量

**Q: 训练速度慢**
A:
- 使用GPU训练
- 增加并行环境数量
- 使用SubprocVecEnv
- 优化环境实现

### 调试技巧

```python
# 启用详细日志
model = MultiCriticSAC("MlpPolicy", env, verbose=2)

# 检查模型参数
print(f"Actor参数数量: {model.actor.get_parameters_count()}")
print(f"训练统计: {model.get_training_stats()}")

# 分析奖励分解
info = env.step(action)[4]
print(f"避障奖励: {info['obstacle_reward']}")
print(f"引导奖励: {info['guidance_reward']}")
print(f"环境奖励: {info['environment_reward']}")

# 监控训练过程
python scripts/monitor.py --log-dir logs/tensorboard/experiment
```

### 性能优化

```python
# 使用编译优化
torch.backends.cudnn.benchmark = True

# 设置线程数
torch.set_num_threads(4)

# 使用混合精度训练（如果支持）
from torch.cuda.amp import autocast, GradScaler

# 优化数据加载
from torch.utils.data import DataLoader
```
