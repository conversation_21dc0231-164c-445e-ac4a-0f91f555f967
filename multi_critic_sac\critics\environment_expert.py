"""
环境影响专家模块

实现环境影响专家评论家组，专门负责评估环境因素对智能体的影响。
考虑洋流、风力等环境因素，优化能量消耗和任务执行效率。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, TYPE_CHECKING
from multi_critic_sac.networks.critic import DoubleCritic
from multi_critic_sac.utils.common import normalize_angle

if TYPE_CHECKING:
    from multi_critic_sac.networks.actor import Actor


class EnvironmentExpert(nn.Module):
    """
    环境影响专家评论家组

    专门负责评估环境因素影响，包括：
    - 洋流影响评估：基于智能体实际朝向和洋流方向计算奖励
    - 风力影响评估：基于智能体实际朝向和风力方向计算奖励
    - 能量效率优化：惩罚过度的能量消耗

    关键特性：
    - 维护航向角跟踪器，基于角速度积分计算智能体朝向
    - 使用精确的物理建模，区分速度方向和朝向方向
    - 支持批量处理，适用于并行训练
    """
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        hidden_sizes: List[int] = [256, 256],
        activation: str = "relu",
        n_critics: int = 2,
        current_scale: float = 0.5,
        wind_scale: float = 0.3,
        energy_penalty_scale: float = 0.1,
        device: torch.device = torch.device("cpu")
    ):
        """
        初始化环境影响专家

        Args:
            state_dim: 状态维度（现在是45维，包含航向角）
            action_dim: 动作维度
            hidden_sizes: 隐藏层大小
            activation: 激活函数
            n_critics: Critic网络数量
            current_scale: 洋流影响缩放因子
            wind_scale: 风力影响缩放因子
            energy_penalty_scale: 能量惩罚缩放因子
            device: 计算设备
        """
        super(EnvironmentExpert, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.current_scale = current_scale
        self.wind_scale = wind_scale
        self.energy_penalty_scale = energy_penalty_scale
        self.device = device
        
        # 创建双Critic网络
        self.critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 目标网络
        self.target_critic = DoubleCritic(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=hidden_sizes,
            activation=activation
        ).to(device)
        
        # 初始化目标网络
        self.target_critic.load_state_dict(self.critic.state_dict())
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.critic.parameters(), lr=3e-4)
        
        print(f"环境影响专家初始化完成: 洋流缩放={current_scale}, "
              f"风力缩放={wind_scale}, 能量惩罚缩放={energy_penalty_scale}")
    
    def extract_environment_data(self, state: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        从状态中提取环境数据

        Args:
            state: 完整状态 [batch_size, state_dim]

        Returns:
            Dict[str, torch.Tensor]: 环境数据字典
        """
        # 环境数据在状态的最后4维：[洋流x, 洋流y, 风x, 风y]
        env_data = state[:, -4:]

        return {
            'current_x': env_data[:, 0],
            'current_y': env_data[:, 1],
            'wind_x': env_data[:, 2],
            'wind_y': env_data[:, 3]
        }
    
    def extract_self_state(self, state: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        从状态中提取自身状态

        Args:
            state: 完整状态 [batch_size, state_dim]

        Returns:
            Dict[str, torch.Tensor]: 自身状态字典
        """
        # 状态向量结构：雷达数据(36维) + 目标向量(2维) + 自身状态(3维) + 环境数据(4维)
        radar_points = 36
        target_vector_dim = 2
        start_idx = radar_points + target_vector_dim
        self_state = state[:, start_idx:start_idx+3]  # 现在是3维

        return {
            'velocity': self_state[:, 0],
            'angular_velocity': self_state[:, 1],
            'heading': self_state[:, 2]  # 新增航向角
        }


    

    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (Q1值, Q2值)
        """
        return self.critic(state, action)
    
    def target_forward(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        目标网络前向传播
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: 目标Q值（取最小值）
        """
        return self.target_critic.min_q(state, action)
    
    def update(self, batch: Dict[str, torch.Tensor], gamma: float = 0.99, actor: Optional['Actor'] = None) -> Dict[str, float]:
        """
        更新环境影响专家网络

        使用环境提供的环境影响分解奖励进行训练。
        按照SAC算法标准，使用Actor网络从下一状态采样动作计算目标Q值。

        Args:
            batch: 训练批次数据
            gamma: 折扣因子
            actor: Actor网络，用于从下一状态采样动作

        Returns:
            Dict[str, float]: 训练指标
        """
        if actor is None:
            raise ValueError("Actor网络不能为None，SAC算法需要Actor网络计算目标Q值")
        states = batch['states']
        actions = batch['actions']
        environment_rewards = batch['environment_rewards']
        next_states = batch['next_states']
        dones = batch['dones']
        
        # 计算当前Q值
        q1, q2 = self.critic(states, actions)
        
        # 计算目标Q值（按照SAC算法标准实现）
        with torch.no_grad():
            # ✅ 正确：使用Actor网络从下一状态采样动作
            next_actions, _ = actor.sample(next_states)
            target_q = self.target_critic.min_q(next_states, next_actions)

            # 🔧 修复维度不匹配：确保奖励张量维度与Q值一致
            environment_rewards_reshaped = environment_rewards.unsqueeze(-1)  # [batch_size] -> [batch_size, 1]
            dones_reshaped = dones.float().unsqueeze(-1)  # [batch_size] -> [batch_size, 1]

            target_q = environment_rewards_reshaped + gamma * (1 - dones_reshaped) * target_q
        
        # 计算损失
        q1_loss = nn.MSELoss()(q1, target_q)
        q2_loss = nn.MSELoss()(q2, target_q)
        total_loss = q1_loss + q2_loss
        
        # 反向传播
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 返回训练指标
        metrics = {
            'environment_q1_loss': q1_loss.item(),
            'environment_q2_loss': q2_loss.item(),
            'environment_total_loss': total_loss.item(),
            'environment_mean_q1': q1.mean().item(),
            'environment_mean_q2': q2.mean().item(),
            'environment_mean_reward': environment_rewards.mean().item()
        }
        
        return metrics
    
    def soft_update_target(self, tau: float = 0.005) -> None:
        """
        软更新目标网络
        
        Args:
            tau: 软更新系数
        """
        for target_param, param in zip(self.target_critic.parameters(), 
                                     self.critic.parameters()):
            target_param.data.copy_(
                tau * param.data + (1.0 - tau) * target_param.data
            )
    
    def get_q_values(self, state: torch.Tensor, action: torch.Tensor) -> torch.Tensor:
        """
        获取Q值（用于融合）
        
        Args:
            state: 输入状态
            action: 输入动作
            
        Returns:
            torch.Tensor: Q值（取最小值）
        """
        return self.critic.min_q(state, action)
    
    def analyze_environment_situation(self, state: torch.Tensor) -> Dict[str, float]:
        """
        分析当前环境情况
        
        Args:
            state: 输入状态
            
        Returns:
            Dict[str, float]: 环境分析结果
        """
        env_data = self.extract_environment_data(state)
        
        # 计算环境强度
        current_magnitude = torch.sqrt(env_data['current_x']**2 + env_data['current_y']**2)
        wind_magnitude = torch.sqrt(env_data['wind_x']**2 + env_data['wind_y']**2)
        
        analysis = {
            'current_magnitude': current_magnitude.mean().item(),
            'wind_magnitude': wind_magnitude.mean().item(),
            'current_direction': torch.atan2(env_data['current_y'], env_data['current_x']).mean().item(),
            'wind_direction': torch.atan2(env_data['wind_y'], env_data['wind_x']).mean().item(),
            'total_env_force': (current_magnitude + wind_magnitude).mean().item()
        }
        
        return analysis
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        保存检查点
        
        Args:
            filepath: 保存路径
        """
        checkpoint = {
            'critic_state_dict': self.critic.state_dict(),
            'target_critic_state_dict': self.target_critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': {
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'current_scale': self.current_scale,
                'wind_scale': self.wind_scale,
                'energy_penalty_scale': self.energy_penalty_scale
            }
        }
        torch.save(checkpoint, filepath)
    
    def load_checkpoint(self, filepath: str) -> None:
        """
        加载检查点
        
        Args:
            filepath: 检查点路径
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.target_critic.load_state_dict(checkpoint['target_critic_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    def __repr__(self) -> str:
        """返回环境影响专家的字符串表示"""
        return (f"EnvironmentExpert(current_scale={self.current_scale}, "
                f"wind_scale={self.wind_scale}, "
                f"energy_penalty_scale={self.energy_penalty_scale})")
