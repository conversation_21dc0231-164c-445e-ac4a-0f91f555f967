Multi-Critic SAC 系统需求规格文档
版本: 2.0
日期: 2025年7月19日

1. 概述
1.1. 项目简介
本文档旨在详细定义一个基于 多评论家 Soft Actor-Critic (Multi-Critic SAC) 的强化学习系统。该系统以 Stable-Baselines3 (SB3) 的 SAC 实现为基础，通过引入多个专门的评论家（Critic）网络，实现对多目标任务的优化。系统设计的核心是奖励分解，将复杂的任务目标分解给不同的评论家专家，包括：避障专家、引导专家和环境影响专家。

1.2. 项目目标
多目标优化：构建一个能够平衡多个（可能相互冲突的）目标的强化学习框架。

提升性能：在复杂的导航和决策任务中，通过专业化的评论家网络提升智能体的学习效率和最终性能。

模块化与可扩展性：设计一个易于扩展的系统，未来可以方便地增减或修改评论家组。

兼容性：确保系统与 Stable-Baselines3 生态兼容，便于复用现有工具和代码。

2. 术语表
| 术语 | 英文 | 解释 |
| :--- | :--- | :--- |
| **演员-评论家** | Actor-Critic | 一类强化学习算法，其中“演员”负责决策（策略），“评论家”负责评估决策的好坏（价值）。 |
| **多评论家SAC** | Multi-Critic SAC | 本项目核心，指拥有多个独立评论家网络的SAC算法变体。 |
| **评论家组** | Critic Group | 针对特定子目标进行优化的一个或多个评论家网络集合。 |
| **避障专家** | Obstacle Avoidance Expert | 负责惩罚碰撞风险、奖励安全行为的评论家组。 |
| **引导专家** | Guidance Expert | 负责奖励智能体朝向目标移动行为的评论家组。 |
| **环境影响专家**| Environment Impact Expert | 负责评估环境因素（如洋流、风）对智能体影响的评论家组。 |
| **状态空间** | State Space | 智能体感知到的环境信息集合，是其决策的依据。 |
| **动作空间** | Action Space | 智能体可以执行的动作集合。 |
| **向量化环境** | Vectorized Environment | 同时运行多个并行的环境实例，以加速数据收集和训练。 |

3. 系统架构概览
系统由一个演员（Actor）网络和三个并行的评论家组（Critic Groups） 组成。

环境（Environment） 向智能体提供完整的状态信息。

演员网络接收状态信息，输出动作指令。

三个评论家组同时接收状态和动作信息，并行地从各自的专业角度（避障、引导、环境）评估该状态-动作对的价值。每个评论家组内部可以包含多个Q网络以稳定训练（如SAC中的双Q网络设计）。

各个评论家组输出的价值（Q-value） 通过一个融合模块（Fusion Module） 进行加权求和，生成一个统一的Q值。

统一的Q值用于计算演员网络和所有评论家网络的损失函数，并进行梯度更新。
```mermaid
graph LR
    %% 环境部分
    Env[环境<br/>Environment]
    
    %% 智能体主体
    subgraph Agent[" 智能体 (Agent) "]
        direction TB
        A[演员网络<br/>Actor]
        
        %% 评论家组水平排列
        subgraph CriticLayer[" 评论家组 (Critic Groups) "]
            direction LR
            C1[避障专家<br/>Obstacle Expert]
            C2[引导专家<br/>Guidance Expert] 
            C3[环境影响专家<br/>Environment Expert]
        end
        
        F[融合模块<br/>Fusion Module]
        
        %% 智能体内部连接
        A --> CriticLayer
        CriticLayer --> F
        F --> A
    end
    
    %% 主要数据流
    Env -->|状态 S| Agent
    Agent -->|动作 A| Env
    Env -->|奖励 R<br/>下一状态 S'| Agent
    
    %% 样式定义
    style Env fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style A fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    style F fill:#fce4ec,stroke:#e91e63,stroke-width:2px
    style C1 fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style C2 fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    style C3 fill:#e0f2f1,stroke:#009688,stroke-width:2px
    style Agent fill:#f5f5f5,stroke:#757575,stroke-width:3px
    style CriticLayer fill:#fafafa,stroke:#bdbdbd,stroke-width:1px,stroke-dasharray: 5 5

4. 功能需求 (Functional Requirements)
FR1: 核心框架 - Multi-Critic SAC
用户故事: 作为强化学习研究者，我希望能够使用多评论家SAC框架来训练智能体处理多个目标，以便在复杂环境中实现更好的性能平衡。

验收标准:

系统必须基于 Stable-Baselines3 的 SAC 算法进行扩展。

系统初始化时，必须能够根据配置创建指定数量的独立评论家组。

在训练的每一步，所有评论家组都必须被独立训练和更新。

FR2: 评论家组 - 避障专家
用户故事: 作为智能体训练者，我希望避障专家能够有效地学习避障策略，以便智能体能够安全地在环境中导航。

验收标准:

奖励逻辑: 避障奖励函数必须根据智能体与最近障碍物的距离进行计算。

当智能体与障碍物发生碰撞时，必须给予一个固定的、较大的负奖励（例如：-50）。

当智能体与障碍物的距离小于预设的安全阈值时，必须给予一个与距离成反比的负奖励。

当智能体成功保持在安全距离之外时，奖励信号应为零。

FR3: 评论家组 - 引导专家
用户故事: 作为路径规划研究者，我希望引导专家能够训练智能体有效地移动到目标终点。

验收标准:

奖励逻辑: 引导奖励函数必须基于智能体与目标点之间距离的变化来计算。

当智能体向目标点靠近时，必须给予正奖励，奖励大小与距离缩减量成正比。

当智能体远离目标点时，必须给予负奖励。

当智能体成功到达目标终点时，必须给予一个固定的、最大的正奖励（例如：+100）。

若在规定时间内未能到达，可以引入一个微小的负向时间惩罚。

FR4: 评论家组 - 环境影响专家
用户故事: 作为海洋环境研究者，我希望环境影响专家能够评估海洋环境要素对智能体的影响，使其能够适应复杂的海洋环境。

验收标准:

奖励逻辑: 环境奖励函数必须基于当前环境因素（洋流、风、浪）对智能体能量消耗或任务执行效率的增益/损耗来计算。

当智能体的航向与洋流/风向一致（顺流/顺风）时，给予正奖励。

当智能体的航向与洋流/风向相反（逆流/逆风）时，给予负奖励。

奖励的绝对值应与环境因素的强度（如流速、风速）成正比。

FR5: 评论家融合与策略更新
用户故事: 作为系统集成者，我希望多个评论家的输出能够有效整合，以生成统一的策略更新信号。

验收标准:

系统必须提供一个融合模块，用于整合所有评论家组的Q值。

默认的融合方法应为加权求和：Q_total=w_1Q_obs+w_2Q_guide+w_3Q_env。

权重 (w_1,w_2,w_3) 必须是可配置的超参数。

演员网络和所有评论家网络的更新必须基于融合后的 Q_total 值进行。

FR6: 状态与动作空间
用户故事: 作为环境建模者，我希望系统能够处理详细的状态和动作表示，以便智能体能够准确感知环境并执行精确控制。

验收标准:

状态空间 (State Space): 必须包含以下所有信息，并进行恰当的坐标系转换。
| 状态分量 | 描述 | 维度 | 坐标系 |
| :--- | :--- | :--- | :--- |
| **雷达数据** | 360° 范围的障碍物距离探测，每10°一个数据点。 | 36 | 局部坐标系 (以智能体为中心) |
| **目标指向** | 当前位置到目标的向量（如方向和距离）。 | 2 | 局部坐标系 (以智能体朝向为x轴) |
| **自身状态** | 智能体的当前速度和角速度。 | 2 | - |
| **环境数据** | 洋流向量（速度和方向）、海浪（大小）、海风（向量）。 | 4+ | 全局或局部坐标系 |
| **总维度** | | **44+** | |

2.  **动作空间 (Action Space)**: 必须输出在局部坐标系下的线速度和角速度控制指令，且数值被归一化到 `[-1, 1]` 区间。

FR7: 训练与环境
用户故事: 作为训练效率优化者，我希望系统支持多线程向量化环境训练，以加速样本收集。

验收标准:

系统必须支持 Stable-Baselines3 的 DummyVecEnv 和 SubprocVecEnv 接口。

用户可以通过配置指定并行环境的数量。

系统必须能正确地从所有并行环境中收集经验数据，并存入统一的经验回放缓冲区（Replay Buffer）。

5. 非功能需求 (Non-Functional Requirements)
NFR1: 兼容性
用户故事: 作为算法开发者，我希望系统能与Stable-Baselines3兼容，以便利用现有的工具。

验收标准:

代码实现应继承自 SB3 的 SAC 类。

系统的超参数配置应兼容标准的 SB3 SAC 参数，并在此基础上增加多评论家相关的配置项（如评论家数量、权重等）。

训练完成后的模型保存和加载应使用 SB3 的标准方法 (.save(), .load())。

NFR2: 性能
用户故事: 我希望系统训练过程高效。

验收标准:

系统在使用 SubprocVecEnv 进行多进程训练时，其样本收集速度（Steps Per Second）应随进程数增加而显著提升。

在单环境模式下，引入多评论家架构所带来的额外计算开销应控制在合理范围内。

NFR3: 可用性与监控
用户故事: 作为实验研究者，我希望系统能提供详细的训练监控和可视化功能，以分析学习过程。

验收标准:

系统必须使用 TensorBoard 或类似的日志工具记录关键指标。

必须记录的指标包括：

总奖励 (Total Reward)。

每个评论家组贡献的分解奖励 (Decomposed Rewards for each critic)。

演员网络损失 (Actor Loss)。

每个评论家组的损失 (Critic Loss for each group)。

日志的命名和组织应清晰，便于区分不同的实验运行。