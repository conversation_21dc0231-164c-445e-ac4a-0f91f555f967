#!/usr/bin/env python3
"""
Multi-Critic SAC训练监控脚本

实时监控训练过程，提供可视化仪表板。
支持TensorBoard日志解析、实时图表更新等功能。

使用方法:
    python scripts/monitor.py --log-dir logs/tensorboard/experiment_name
    python scripts/monitor.py --log-dir logs/tensorboard/experiment_name --update-interval 10

作者: Multi-Critic SAC Team
"""

import argparse
import os
import sys
import time
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac.utils.visualization import TrainingVisualizer, load_tensorboard_data
from multi_critic_sac.utils.common import setup_logger


class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, log_dir: str, update_interval: int = 5):
        """
        初始化监控器
        
        Args:
            log_dir: 日志目录
            update_interval: 更新间隔（秒）
        """
        self.log_dir = Path(log_dir)
        self.update_interval = update_interval
        self.logger = setup_logger("TrainingMonitor")
        
        # 可视化器
        self.visualizer = TrainingVisualizer()
        
        # 数据存储
        self.data_history = {}
        self.last_update_time = 0
        
        # 图形设置
        plt.ion()  # 开启交互模式
        self.fig, self.axes = plt.subplots(2, 2, figsize=(15, 10))
        self.fig.suptitle('Multi-Critic SAC 训练监控', fontsize=16)
        
        self.logger.info(f"监控器初始化完成，日志目录: {log_dir}")
    
    def load_latest_data(self) -> Dict[str, any]:
        """加载最新数据"""
        try:
            # 从TensorBoard日志加载
            tb_data = load_tensorboard_data(str(self.log_dir))
            
            # 从JSON文件加载（如果存在）
            json_files = list(self.log_dir.glob("*.json"))
            json_data = {}
            
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                        json_data.update(file_data)
                except Exception as e:
                    self.logger.warning(f"无法加载JSON文件 {json_file}: {e}")
            
            # 合并数据
            combined_data = {**tb_data, **json_data}
            
            return combined_data
        
        except Exception as e:
            self.logger.error(f"加载数据时出错: {e}")
            return {}
    
    def update_plots(self, data: Dict[str, any]) -> None:
        """更新图表"""
        if not data:
            return
        
        # 清除之前的图表
        for ax in self.axes.flat:
            ax.clear()
        
        # 1. 总奖励曲线
        if 'episode/total_reward' in data:
            rewards = data['episode/total_reward']
            self.axes[0, 0].plot(rewards, 'b-', alpha=0.7)
            self.axes[0, 0].set_title('总奖励')
            self.axes[0, 0].set_xlabel('回合')
            self.axes[0, 0].set_ylabel('奖励')
            self.axes[0, 0].grid(True, alpha=0.3)
            
            # 添加移动平均线
            if len(rewards) > 10:
                window = min(50, len(rewards) // 4)
                moving_avg = np.convolve(rewards, np.ones(window)/window, mode='valid')
                self.axes[0, 0].plot(range(window-1, len(rewards)), moving_avg, 
                                   'r-', alpha=0.8, label=f'移动平均({window})')
                self.axes[0, 0].legend()
        
        # 2. 分解奖励
        reward_keys = ['episode/obstacle_reward', 'episode/guidance_reward', 'episode/environment_reward']
        labels = ['避障奖励', '引导奖励', '环境奖励']
        colors = ['red', 'green', 'blue']
        
        for key, label, color in zip(reward_keys, labels, colors):
            if key in data:
                self.axes[0, 1].plot(data[key], label=label, color=color, alpha=0.7)
        
        self.axes[0, 1].set_title('分解奖励')
        self.axes[0, 1].set_xlabel('回合')
        self.axes[0, 1].set_ylabel('奖励')
        self.axes[0, 1].legend()
        self.axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 损失函数
        loss_keys = ['training/actor_loss', 'training/obstacle_total_loss', 
                    'training/guidance_total_loss', 'training/environment_total_loss']
        loss_labels = ['Actor损失', '避障损失', '引导损失', '环境损失']
        
        for key, label in zip(loss_keys, loss_labels):
            if key in data:
                self.axes[1, 0].plot(data[key], label=label, alpha=0.7)
        
        self.axes[1, 0].set_title('损失函数')
        self.axes[1, 0].set_xlabel('训练步数')
        self.axes[1, 0].set_ylabel('损失')
        self.axes[1, 0].legend()
        self.axes[1, 0].grid(True, alpha=0.3)
        self.axes[1, 0].set_yscale('log')  # 对数坐标
        
        # 4. 训练统计
        stats_text = self._generate_stats_text(data)
        self.axes[1, 1].text(0.1, 0.9, stats_text, transform=self.axes[1, 1].transAxes,
                           fontsize=10, verticalalignment='top', fontfamily='monospace')
        self.axes[1, 1].set_title('训练统计')
        self.axes[1, 1].axis('off')
        
        # 更新图形
        plt.tight_layout()
        plt.draw()
        plt.pause(0.01)
    
    def _generate_stats_text(self, data: Dict[str, any]) -> str:
        """生成统计文本"""
        stats = []
        
        # 基本信息
        if 'training/num_timesteps' in data:
            timesteps = data['training/num_timesteps']
            if timesteps:
                stats.append(f"训练步数: {timesteps[-1]:,}")
        
        if 'training/n_updates' in data:
            updates = data['training/n_updates']
            if updates:
                stats.append(f"更新次数: {updates[-1]:,}")
        
        # 最新奖励
        if 'episode/total_reward' in data:
            rewards = data['episode/total_reward']
            if rewards:
                recent_rewards = rewards[-10:] if len(rewards) >= 10 else rewards
                stats.append(f"最近平均奖励: {np.mean(recent_rewards):.2f}")
                stats.append(f"最高奖励: {np.max(rewards):.2f}")
        
        # 成功率（如果有）
        if 'episode/success_rate' in data:
            success_rates = data['episode/success_rate']
            if success_rates:
                stats.append(f"成功率: {success_rates[-1]:.1%}")
        
        # 缓冲区信息
        if 'buffer/size' in data:
            buffer_sizes = data['buffer/size']
            if buffer_sizes:
                stats.append(f"缓冲区大小: {buffer_sizes[-1]:,}")
        
        if 'buffer/utilization' in data:
            utilizations = data['buffer/utilization']
            if utilizations:
                stats.append(f"缓冲区利用率: {utilizations[-1]:.1%}")
        
        # 学习率
        if 'training/learning_rate' in data:
            lrs = data['training/learning_rate']
            if lrs:
                stats.append(f"学习率: {lrs[-1]:.2e}")
        
        # 熵系数
        if 'training/ent_coef' in data:
            ent_coefs = data['training/ent_coef']
            if ent_coefs:
                stats.append(f"熵系数: {ent_coefs[-1]:.3f}")
        
        return '\n'.join(stats) if stats else "暂无数据"
    
    def run_monitoring(self) -> None:
        """运行监控"""
        self.logger.info("开始监控训练过程...")
        
        try:
            while True:
                current_time = time.time()
                
                # 检查是否需要更新
                if current_time - self.last_update_time >= self.update_interval:
                    # 加载最新数据
                    data = self.load_latest_data()
                    
                    if data:
                        # 更新图表
                        self.update_plots(data)
                        self.last_update_time = current_time
                        self.logger.info("监控数据已更新")
                    else:
                        self.logger.warning("未找到有效数据")
                
                # 短暂休眠
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.logger.info("监控被用户中断")
        except Exception as e:
            self.logger.error(f"监控过程中发生错误: {e}")
        finally:
            plt.ioff()
            plt.close('all')
    
    def generate_report(self, save_path: str = None) -> None:
        """生成训练报告"""
        if save_path is None:
            save_path = self.log_dir / "training_report.html"
        
        # 加载数据
        data = self.load_latest_data()
        
        if not data:
            self.logger.warning("无数据可生成报告")
            return
        
        # 生成HTML报告
        html_content = self._generate_html_report(data)
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"训练报告已生成: {save_path}")
    
    def _generate_html_report(self, data: Dict[str, any]) -> str:
        """生成HTML报告"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Multi-Critic SAC 训练报告</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .section { margin: 20px 0; }
                .metric { display: inline-block; margin: 10px; padding: 10px; 
                         background-color: #e8f4f8; border-radius: 5px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Multi-Critic SAC 训练报告</h1>
                <p>生成时间: {timestamp}</p>
            </div>
        """.format(timestamp=time.strftime("%Y-%m-%d %H:%M:%S"))
        
        # 添加统计信息
        stats_text = self._generate_stats_text(data)
        html += f"""
            <div class="section">
                <h2>训练统计</h2>
                <pre>{stats_text}</pre>
            </div>
        """
        
        # 添加性能指标表格
        if 'episode/total_reward' in data:
            rewards = data['episode/total_reward']
            html += f"""
            <div class="section">
                <h2>性能指标</h2>
                <table>
                    <tr><th>指标</th><th>值</th></tr>
                    <tr><td>总回合数</td><td>{len(rewards)}</td></tr>
                    <tr><td>平均奖励</td><td>{np.mean(rewards):.2f}</td></tr>
                    <tr><td>最高奖励</td><td>{np.max(rewards):.2f}</td></tr>
                    <tr><td>最低奖励</td><td>{np.min(rewards):.2f}</td></tr>
                    <tr><td>奖励标准差</td><td>{np.std(rewards):.2f}</td></tr>
                </table>
            </div>
            """
        
        html += """
        </body>
        </html>
        """
        
        return html


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Multi-Critic SAC训练监控脚本")
    parser.add_argument(
        "--log-dir", "-l",
        type=str,
        required=True,
        help="日志目录路径"
    )
    parser.add_argument(
        "--update-interval", "-u",
        type=int,
        default=5,
        help="更新间隔（秒）"
    )
    parser.add_argument(
        "--generate-report", "-r",
        action="store_true",
        help="生成训练报告并退出"
    )
    
    args = parser.parse_args()
    
    # 检查日志目录是否存在
    if not os.path.exists(args.log_dir):
        print(f"错误: 日志目录不存在: {args.log_dir}")
        sys.exit(1)
    
    # 创建监控器
    monitor = TrainingMonitor(args.log_dir, args.update_interval)
    
    if args.generate_report:
        # 生成报告
        monitor.generate_report()
    else:
        # 运行监控
        monitor.run_monitoring()


if __name__ == "__main__":
    main()
