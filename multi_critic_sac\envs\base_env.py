"""
基础环境模块

定义Multi-Critic SAC环境的基础接口和通用功能。
提供标准的Gymnasium环境接口实现。

作者: Multi-Critic SAC Team
"""

import gymnasium as gym
import numpy as np
from typing import Dict, Tuple, Any, Optional, Union
from abc import ABC, abstractmethod
import logging


class BaseEnv(gym.Env, ABC):
    """
    Multi-Critic SAC基础环境类
    
    定义了Multi-Critic SAC算法所需的环境接口，
    包括44+维状态空间和连续动作空间的标准实现。
    """
    
    def __init__(
        self,
        max_episode_steps: int = 1000,
        render_mode: Optional[str] = None,
        seed: Optional[int] = None
    ):
        """
        初始化基础环境
        
        Args:
            max_episode_steps: 最大回合步数
            render_mode: 渲染模式
            seed: 随机种子
        """
        super().__init__()
        
        self.max_episode_steps = max_episode_steps
        self.render_mode = render_mode
        self.current_step = 0
        
        # 设置随机种子
        if seed is not None:
            self.seed(seed)
        
        # 初始化日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 定义状态空间维度（44+维）
        self.state_dim = self._get_state_dim()
        
        # 定义动作空间维度（连续2维：线速度，角速度）
        self.action_dim = 2
        
        # 初始化观测和动作空间
        self._init_spaces()
        
        # 环境状态
        self.reset_info = {}
        
        print(f"{self.__class__.__name__}环境初始化完成: "
              f"状态维度={self.state_dim}, 动作维度={self.action_dim}")
    
    def _get_state_dim(self) -> int:
        """
        获取状态空间维度

        Returns:
            int: 状态维度
        """
        # 45维状态空间组成：
        # - 雷达数据: 36维 (360°/10° = 36个数据点)
        # - 目标指向: 2维 (距离, 角度)
        # - 自身状态: 3维 (速度, 角速度, 航向角)
        # - 环境数据: 4维 (洋流x,y, 风x,y)
        # 总计: 36 + 2 + 3 + 4 = 45维
        return 45
    
    def _init_spaces(self) -> None:
        """初始化观测空间和动作空间"""
        # 观测空间：44维连续空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.state_dim,),
            dtype=np.float32
        )
        
        # 动作空间：2维连续空间 [-1, 1]
        self.action_space = gym.spaces.Box(
            low=np.array([-1.0, -1.0], dtype=np.float32),
            high=np.array([1.0, 1.0], dtype=np.float32),
            dtype=np.float32
        )
    
    @abstractmethod
    def _get_observation(self) -> np.ndarray:
        """
        获取当前观测
        
        Returns:
            np.ndarray: 44维观测向量
        """
        pass
    
    @abstractmethod
    def _calculate_reward(self, action: np.ndarray) -> Dict[str, float]:
        """
        计算奖励（包括分解奖励）
        
        Args:
            action: 执行的动作
            
        Returns:
            Dict[str, float]: 奖励字典，包含总奖励和分解奖励
        """
        pass
    
    @abstractmethod
    def _is_terminated(self) -> bool:
        """
        检查是否终止
        
        Returns:
            bool: 是否终止
        """
        pass
    
    @abstractmethod
    def _is_truncated(self) -> bool:
        """
        检查是否截断
        
        Returns:
            bool: 是否截断
        """
        pass
    
    @abstractmethod
    def _reset_environment(self) -> None:
        """重置环境状态"""
        pass
    
    def reset(self, seed: Optional[int] = None, 
             options: Optional[Dict[str, Any]] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        重置环境
        
        Args:
            seed: 随机种子
            options: 重置选项
            
        Returns:
            Tuple[np.ndarray, Dict[str, Any]]: (观测, 信息)
        """
        super().reset(seed=seed)
        
        # 重置步数
        self.current_step = 0
        
        # 重置环境状态
        self._reset_environment()
        
        # 获取初始观测
        observation = self._get_observation()
        
        # 重置信息
        self.reset_info = {
            'episode_step': self.current_step,
            'max_steps': self.max_episode_steps
        }
        
        return observation, self.reset_info
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """
        执行一步动作
        
        Args:
            action: 动作向量
            
        Returns:
            Tuple: (观测, 奖励, 终止, 截断, 信息)
        """
        # 确保动作在有效范围内
        action = np.clip(action, self.action_space.low, self.action_space.high)
        
        # 执行动作（由子类实现具体逻辑）
        self._execute_action(action)
        
        # 更新步数
        self.current_step += 1
        
        # 获取新观测
        observation = self._get_observation()
        
        # 计算奖励
        reward_dict = self._calculate_reward(action)
        total_reward = reward_dict['total_reward']
        
        # 检查终止和截断条件
        terminated = self._is_terminated()
        truncated = self._is_truncated() or (self.current_step >= self.max_episode_steps)
        
        # 构建信息字典
        info = {
            'episode_step': self.current_step,
            'total_reward': total_reward,
            'obstacle_reward': reward_dict.get('obstacle_reward', 0.0),
            'guidance_reward': reward_dict.get('guidance_reward', 0.0),
            'environment_reward': reward_dict.get('environment_reward', 0.0),
            'terminated': terminated,
            'truncated': truncated
        }
        
        return observation, total_reward, terminated, truncated, info
    
    @abstractmethod
    def _execute_action(self, action: np.ndarray) -> None:
        """
        执行动作
        
        Args:
            action: 动作向量
        """
        pass
    
    def render(self) -> Optional[Union[np.ndarray, str]]:
        """
        渲染环境
        
        Returns:
            Optional[Union[np.ndarray, str]]: 渲染结果
        """
        if self.render_mode == "human":
            # 人类可视化渲染
            return self._render_human()
        elif self.render_mode == "rgb_array":
            # 返回RGB数组
            return self._render_rgb_array()
        else:
            return None
    
    def _render_human(self) -> None:
        """人类可视化渲染（由子类实现）"""
        pass
    
    def _render_rgb_array(self) -> np.ndarray:
        """
        RGB数组渲染（由子类实现）
        
        Returns:
            np.ndarray: RGB图像数组
        """
        return np.zeros((400, 400, 3), dtype=np.uint8)
    
    def close(self) -> None:
        """关闭环境"""
        pass
    
    def seed(self, seed: Optional[int] = None) -> list:
        """
        设置随机种子

        Args:
            seed: 随机种子

        Returns:
            list: 种子列表
        """
        if seed is not None:
            np.random.seed(seed)
            return [seed]
        return []
    
    def get_state_info(self) -> Dict[str, Any]:
        """
        获取状态信息
        
        Returns:
            Dict[str, Any]: 状态信息字典
        """
        observation = self._get_observation()
        
        # 解析状态向量
        radar_data = observation[:36]
        target_vector = observation[36:38]
        self_state = observation[38:41]  # 现在是3维
        env_data = observation[41:45]    # 索引向后移动

        return {
            'radar_data': radar_data,
            'target_distance': np.linalg.norm(target_vector),
            'target_angle': np.arctan2(target_vector[1], target_vector[0]),
            'velocity': self_state[0],
            'angular_velocity': self_state[1],
            'heading': self_state[2],  # 新增航向角
            'current_x': env_data[0],
            'current_y': env_data[1],
            'wind_x': env_data[2],
            'wind_y': env_data[3],
            'min_obstacle_distance': np.min(radar_data),
            'current_step': self.current_step
        }
    
    def get_action_info(self, action: np.ndarray) -> Dict[str, float]:
        """
        获取动作信息
        
        Args:
            action: 动作向量
            
        Returns:
            Dict[str, float]: 动作信息字典
        """
        return {
            'linear_velocity': float(action[0]),
            'angular_velocity': float(action[1]),
            'action_magnitude': float(np.linalg.norm(action))
        }
    
    def __repr__(self) -> str:
        """返回环境的字符串表示"""
        return (f"{self.__class__.__name__}(state_dim={self.state_dim}, "
                f"action_dim={self.action_dim}, "
                f"max_episode_steps={self.max_episode_steps})")
