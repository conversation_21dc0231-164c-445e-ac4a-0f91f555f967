"""
Multi-Critic SAC算法核心实现

基于Stable-Baselines3的SAC算法扩展，实现多评论家架构。
包含三个专门的评论家组：避障专家、引导专家、环境影响专家。

作者: Multi-Critic SAC Team
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from stable_baselines3.common.base_class import BaseAlgorithm
from stable_baselines3.common.policies import BasePolicy, ActorCriticPolicy
from stable_baselines3.common.type_aliases import GymEnv
from stable_baselines3.common.utils import polyak_update
import gymnasium as gym

from multi_critic_sac.networks.actor import Actor
from multi_critic_sac.critics.obstacle_expert import ObstacleExpert
from multi_critic_sac.critics.guidance_expert import GuidanceExpert
from multi_critic_sac.critics.environment_expert import EnvironmentExpert
from multi_critic_sac.critics.fusion_module import FusionModule
from multi_critic_sac.utils.replay_buffer import ReplayBuffer
from multi_critic_sac.utils.logger import Logger
from multi_critic_sac.utils.common import get_device, soft_update
from multi_critic_sac.config.config_manager import Config


class MultiCriticSAC(BaseAlgorithm):
    """
    Multi-Critic SAC算法

    基于SAC算法的多评论家扩展，通过专门的评论家组
    处理多目标优化问题。
    """

    # 策略别名注册
    policy_aliases: Dict[str, type] = {
        "MlpPolicy": ActorCriticPolicy,
    }
    
    def __init__(
        self,
        policy: Union[str, type] = "MlpPolicy",
        env: Optional[GymEnv] = None,
        learning_rate: float = 3e-4,
        buffer_size: int = 1000000,
        learning_starts: int = 100,
        batch_size: int = 256,
        tau: float = 0.005,
        gamma: float = 0.99,
        train_freq: int = 1,
        gradient_steps: int = 1,
        ent_coef: Union[str, float] = "auto",
        target_update_interval: int = 1,
        target_entropy: Union[str, float] = "auto",
        critic_weights: Dict[str, float] = None,
        use_sde: bool = False,
        sde_sample_freq: int = -1,
        use_sde_at_warmup: bool = False,
        tensorboard_log: Optional[str] = None,
        policy_kwargs: Optional[Dict[str, Any]] = None,
        verbose: int = 0,
        seed: Optional[int] = None,
        device: Union[torch.device, str] = "auto",
        _init_setup_model: bool = True,
        config: Optional[Config] = None
    ):
        """
        初始化Multi-Critic SAC算法
        
        Args:
            policy: 策略类型
            env: 环境
            learning_rate: 学习率
            buffer_size: 经验回放缓冲区大小
            learning_starts: 开始学习的步数
            batch_size: 批次大小
            tau: 软更新参数
            gamma: 折扣因子
            train_freq: 训练频率
            gradient_steps: 梯度步数
            ent_coef: 熵系数
            target_update_interval: 目标网络更新间隔
            target_entropy: 目标熵
            critic_weights: 评论家权重
            use_sde: 是否使用状态依赖探索
            sde_sample_freq: SDE采样频率
            use_sde_at_warmup: 预热时是否使用SDE
            tensorboard_log: TensorBoard日志路径
            policy_kwargs: 策略参数
            verbose: 详细程度
            seed: 随机种子
            device: 计算设备
            _init_setup_model: 是否初始化模型
            config: 配置对象
        """
        # 设置默认评论家权重
        if critic_weights is None:
            critic_weights = {'obstacle': 0.4, 'guidance': 0.4, 'environment': 0.2}

        self.critic_weights = critic_weights
        self.config = config

        # 保存SAC特有的参数
        self.use_sde_at_warmup = use_sde_at_warmup

        # 初始化基类
        super().__init__(
            policy=policy,
            env=env,
            learning_rate=learning_rate,
            policy_kwargs=policy_kwargs,
            verbose=verbose,
            device=device,
            use_sde=use_sde,
            sde_sample_freq=sde_sample_freq,
            tensorboard_log=tensorboard_log,
            seed=seed,
            supported_action_spaces=(gym.spaces.Box,),
        )
        
        # SAC特定参数
        self.buffer_size = buffer_size
        self.learning_starts = learning_starts
        self.batch_size = batch_size
        self.tau = tau
        self.gamma = gamma
        self.train_freq = train_freq
        self.gradient_steps = gradient_steps
        self.target_update_interval = target_update_interval
        
        # 熵相关参数
        self.ent_coef = ent_coef
        self.target_entropy = target_entropy
        self.log_ent_coef = None
        self.ent_coef_optimizer = None
        
        # 网络和组件
        self.actor = None
        self.obstacle_expert = None
        self.guidance_expert = None
        self.environment_expert = None
        self.fusion_module = None
        self.replay_buffer = None
        
        # 优化器
        self.actor_optimizer = None
        
        # 训练统计
        self.num_timesteps = 0
        self._n_updates = 0
        
        if _init_setup_model:
            self._setup_model()
    
    def _setup_model(self) -> None:
        """设置模型"""
        # 获取环境信息
        if self.env is not None:
            self.observation_space = self.env.observation_space
            self.action_space = self.env.action_space
        
        # 设置设备
        self.device = get_device(use_cuda=True)
        
        # 获取状态和动作维度
        state_dim = self.observation_space.shape[0]
        action_dim = self.action_space.shape[0]
        
        # 创建Actor网络
        self.actor = Actor(
            state_dim=state_dim,
            action_dim=action_dim,
            hidden_sizes=[256, 256],
            activation="relu"
        ).to(self.device)
        
        # 创建评论家专家
        self.obstacle_expert = ObstacleExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=self.device
        )
        
        self.guidance_expert = GuidanceExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=self.device
        )
        
        self.environment_expert = EnvironmentExpert(
            state_dim=state_dim,
            action_dim=action_dim,
            device=self.device
        )
        
        # 创建融合模块
        self.fusion_module = FusionModule(
            obstacle_expert=self.obstacle_expert,
            guidance_expert=self.guidance_expert,
            environment_expert=self.environment_expert,
            fusion_weights=self.critic_weights,
            device=self.device
        )
        
        # 创建经验回放缓冲区
        self.replay_buffer = ReplayBuffer(
            capacity=self.buffer_size,
            state_dim=state_dim,
            action_dim=action_dim,
            device=self.device
        )
        
        # 创建优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.learning_rate)
        
        # 设置熵系数
        self._setup_entropy_coefficient()
        
        # 创建日志记录器
        if self.tensorboard_log:
            self.logger = Logger(
                log_dir=self.tensorboard_log,
                experiment_name=f"multi_critic_sac_{self.num_timesteps}",
                use_tensorboard=True
            )
        
        print("Multi-Critic SAC模型设置完成")
    
    def _setup_entropy_coefficient(self) -> None:
        """设置熵系数"""
        if isinstance(self.ent_coef, str) and self.ent_coef.startswith("auto"):
            # 自动调整熵系数
            if self.target_entropy == "auto":
                self.target_entropy = -np.prod(self.action_space.shape).astype(np.float32)
            else:
                self.target_entropy = float(self.target_entropy)
            
            # 创建可学习的熵系数
            self.log_ent_coef = torch.log(torch.ones(1, device=self.device)).requires_grad_(True)
            self.ent_coef_optimizer = optim.Adam([self.log_ent_coef], lr=self.learning_rate)
        else:
            # 固定熵系数
            self.ent_coef = float(self.ent_coef)
            self.target_entropy = None
    
    def predict(
        self,
        observation: np.ndarray,
        state: Optional[Tuple[np.ndarray, ...]] = None,
        episode_start: Optional[np.ndarray] = None,
        deterministic: bool = False,
    ) -> Tuple[np.ndarray, Optional[Tuple[np.ndarray, ...]]]:
        """
        预测动作
        
        Args:
            observation: 观测
            state: 状态（未使用）
            episode_start: 回合开始标志（未使用）
            deterministic: 是否确定性预测
            
        Returns:
            Tuple[np.ndarray, Optional[Tuple[np.ndarray, ...]]]: (动作, 状态)
        """
        if not isinstance(observation, torch.Tensor):
            observation = torch.FloatTensor(observation).to(self.device)
        
        if observation.dim() == 1:
            observation = observation.unsqueeze(0)
        
        with torch.no_grad():
            action, _ = self.actor.sample(observation, deterministic=deterministic)
            action = action.cpu().numpy()
        
        return action, state

    def learn(
        self,
        total_timesteps: int,
        callback=None,
        log_interval: int = 100,
        tb_log_name: str = "MultiCriticSAC",
        reset_num_timesteps: bool = True,
        progress_bar: bool = False,
    ):
        """
        学习算法

        Args:
            total_timesteps: 总训练步数
            callback: 回调函数
            log_interval: 日志间隔
            tb_log_name: TensorBoard日志名称
            reset_num_timesteps: 是否重置时间步数
            progress_bar: 是否显示进度条
        """
        if reset_num_timesteps:
            self.num_timesteps = 0
            self._n_updates = 0

        # 开始训练循环
        obs = self.env.reset()
        if isinstance(obs, tuple):
            obs = obs[0]

        for step in range(total_timesteps):
            # 选择动作
            if self.num_timesteps < self.learning_starts:
                # 随机探索
                action = self.action_space.sample()
            else:
                action, _ = self.predict(obs, deterministic=False)
                action = action[0] if action.ndim > 1 else action

            # 执行动作
            new_obs, reward, terminated, truncated, info = self.env.step(action)
            done = terminated or truncated

            # 存储经验
            self.replay_buffer.add(
                state=obs,
                action=action,
                reward=reward,
                next_state=new_obs,
                done=done,
                obstacle_reward=info.get('obstacle_reward', 0.0),
                guidance_reward=info.get('guidance_reward', 0.0),
                environment_reward=info.get('environment_reward', 0.0)
            )

            obs = new_obs
            self.num_timesteps += 1

            # 训练
            if (self.num_timesteps >= self.learning_starts and
                self.num_timesteps % self.train_freq == 0):

                for _ in range(self.gradient_steps):
                    self._train_step()

            # 重置环境
            if done:
                obs = self.env.reset()
                if isinstance(obs, tuple):
                    obs = obs[0]

            # 日志记录
            if self.num_timesteps % log_interval == 0 and self.logger:
                self._log_training_info()

        return self

    def _train_step(self) -> None:
        """执行一步训练"""
        if not self.replay_buffer.is_ready(self.batch_size):
            return

        # 采样批次数据
        batch = self.replay_buffer.sample(self.batch_size)

        # 更新评论家专家（传递Actor网络用于正确计算目标Q值）
        obstacle_metrics = self.obstacle_expert.update(batch, self.gamma, self.actor)
        guidance_metrics = self.guidance_expert.update(batch, self.gamma, self.actor)
        environment_metrics = self.environment_expert.update(batch, self.gamma, self.actor)

        # 更新Actor
        actor_metrics = self._update_actor(batch)

        # 更新熵系数
        ent_metrics = self._update_entropy_coefficient(batch)

        # 软更新目标网络
        if self._n_updates % self.target_update_interval == 0:
            self.obstacle_expert.soft_update_target(self.tau)
            self.guidance_expert.soft_update_target(self.tau)
            self.environment_expert.soft_update_target(self.tau)

        self._n_updates += 1

        # 记录训练指标
        if self.logger:
            all_metrics = {**obstacle_metrics, **guidance_metrics,
                          **environment_metrics, **actor_metrics, **ent_metrics}
            self.logger.log_training_metrics(self.num_timesteps, all_metrics)

    def _update_actor(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """更新Actor网络"""
        states = batch['states']

        # 采样新动作
        actions, log_probs = self.actor.sample(states)

        # 获取融合Q值
        q_values = self.fusion_module(states, actions)

        # 计算熵系数
        if self.log_ent_coef is not None:
            ent_coef = torch.exp(self.log_ent_coef)
        else:
            ent_coef = self.ent_coef

        # Actor损失
        actor_loss = (ent_coef * log_probs - q_values).mean()

        # 反向传播
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        return {
            'actor_loss': actor_loss.item(),
            'mean_log_prob': log_probs.mean().item(),
            'ent_coef': float(ent_coef) if isinstance(ent_coef, torch.Tensor) else ent_coef
        }

    def _update_entropy_coefficient(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """更新熵系数"""
        if self.log_ent_coef is None:
            return {}

        states = batch['states']

        with torch.no_grad():
            _, log_probs = self.actor.sample(states)

        # 熵损失
        ent_coef_loss = -(self.log_ent_coef * (log_probs + self.target_entropy).detach()).mean()

        # 反向传播
        self.ent_coef_optimizer.zero_grad()
        ent_coef_loss.backward()
        self.ent_coef_optimizer.step()

        return {
            'ent_coef_loss': ent_coef_loss.item(),
            'target_entropy': self.target_entropy
        }

    def _log_training_info(self) -> None:
        """记录训练信息"""
        if not self.logger:
            return

        # 获取缓冲区统计信息
        buffer_stats = self.replay_buffer.get_statistics()

        # 记录基本信息
        self.logger.log_scalar('training/num_timesteps', self.num_timesteps)
        self.logger.log_scalar('training/n_updates', self._n_updates)

        # 记录缓冲区信息
        for key, value in buffer_stats.items():
            self.logger.log_scalar(f'buffer/{key}', value)

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        checkpoint = {
            'actor_state_dict': self.actor.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'num_timesteps': self.num_timesteps,
            'n_updates': self._n_updates,
            'critic_weights': self.critic_weights,
            'hyperparameters': {
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size,
                'tau': self.tau,
                'gamma': self.gamma,
                'buffer_size': self.buffer_size
            }
        }

        # 保存熵系数
        if self.log_ent_coef is not None:
            checkpoint['log_ent_coef'] = self.log_ent_coef
            checkpoint['ent_coef_optimizer_state_dict'] = self.ent_coef_optimizer.state_dict()

        torch.save(checkpoint, path)

        # 保存评论家专家
        self.obstacle_expert.save_checkpoint(f"{path}_obstacle_expert.pt")
        self.guidance_expert.save_checkpoint(f"{path}_guidance_expert.pt")
        self.environment_expert.save_checkpoint(f"{path}_environment_expert.pt")
        self.fusion_module.save_checkpoint(f"{path}_fusion_module.pt")

        print(f"模型已保存到: {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 模型路径
        """
        checkpoint = torch.load(path, map_location=self.device)

        # 加载Actor
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])

        # 加载训练状态
        self.num_timesteps = checkpoint['num_timesteps']
        self._n_updates = checkpoint['n_updates']
        self.critic_weights = checkpoint['critic_weights']

        # 加载熵系数
        if 'log_ent_coef' in checkpoint:
            self.log_ent_coef = checkpoint['log_ent_coef']
            self.ent_coef_optimizer.load_state_dict(checkpoint['ent_coef_optimizer_state_dict'])

        # 加载评论家专家
        self.obstacle_expert.load_checkpoint(f"{path}_obstacle_expert.pt")
        self.guidance_expert.load_checkpoint(f"{path}_guidance_expert.pt")
        self.environment_expert.load_checkpoint(f"{path}_environment_expert.pt")
        self.fusion_module.load_checkpoint(f"{path}_fusion_module.pt")

        print(f"模型已从{path}加载")

    def get_training_stats(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        stats = {
            'num_timesteps': self.num_timesteps,
            'n_updates': self._n_updates,
            'buffer_size': len(self.replay_buffer),
            'critic_weights': self.critic_weights
        }

        if self.replay_buffer:
            stats.update(self.replay_buffer.get_statistics())

        return stats
