# Multi-Critic SAC (多评论家软演员-评论家算法)

基于Stable-Baselines3的多评论家Soft Actor-Critic强化学习算法实现，专门用于多目标优化任务。

## 项目概述

Multi-Critic SAC是一个创新的强化学习框架，通过引入多个专门的评论家网络来处理复杂的多目标任务。该系统将复杂的任务目标分解给不同的评论家专家：

- **避障专家**: 负责惩罚碰撞风险，奖励安全行为
- **引导专家**: 负责奖励智能体朝向目标移动的行为  
- **环境影响专家**: 负责评估环境因素对智能体的影响

## 主要特性

- 🎯 **多目标优化**: 平衡多个可能相互冲突的目标
- 🚀 **高性能**: 支持多进程向量化环境训练
- 🔧 **模块化设计**: 易于扩展和修改评论家组
- 📊 **完整监控**: 集成TensorBoard日志和可视化
- 🔄 **SB3兼容**: 与Stable-Baselines3生态完全兼容

## 安装

### 环境要求

- Python 3.11+
- PyTorch 2.6.0+cu118
- CUDA 11.8+ (可选，用于GPU加速)

### 安装步骤

1. 克隆项目：
```bash
git clone https://github.com/multi-critic-sac/multi-critic-sac.git
cd multi-critic-sac
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 安装项目：
```bash
pip install -e .
```

## 快速开始

### 基础训练示例

```python
from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from stable_baselines3.common.vec_env import DummyVecEnv

# 创建环境
env = DummyVecEnv([lambda: NavigationEnv()])

# 创建Multi-Critic SAC模型
model = MultiCriticSAC(
    policy="MlpPolicy",
    env=env,
    critic_weights=[0.4, 0.4, 0.2],  # 避障、引导、环境权重
    verbose=1,
    tensorboard_log="./logs/"
)

# 开始训练
model.learn(total_timesteps=100000)

# 保存模型
model.save("multi_critic_sac_model")
```

### 配置文件训练

```bash
python scripts/train.py --config configs/default.yaml
```

## 项目结构

```
multi-critic-sac/
├── multi_critic_sac/          # 主要源代码
│   ├── algorithms/             # 算法实现
│   ├── networks/              # 神经网络模型
│   ├── critics/               # 评论家专家
│   ├── envs/                  # 环境接口
│   ├── utils/                 # 工具函数
│   └── config/                # 配置管理
├── scripts/                   # 训练和评估脚本
├── configs/                   # 配置文件
├── tests/                     # 测试代码
├── docs/                      # 文档
└── examples/                  # 使用示例
```

## 配置说明

主要配置参数：

- `critic_weights`: 各评论家权重 [避障, 引导, 环境]
- `learning_rate`: 学习率
- `buffer_size`: 经验回放缓冲区大小
- `batch_size`: 批次大小
- `tau`: 软更新参数

详细配置请参考 `configs/default.yaml`

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 贡献

欢迎提交Issue和Pull Request！

## 引用

如果您在研究中使用了本项目，请引用：

```bibtex
@software{multi_critic_sac,
  title={Multi-Critic SAC: A Multi-Objective Reinforcement Learning Framework},
  author={Multi-Critic SAC Team},
  year={2025},
  url={https://github.com/multi-critic-sac/multi-critic-sac}
}
```
