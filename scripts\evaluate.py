#!/usr/bin/env python3
"""
Multi-Critic SAC评估脚本

评估训练好的Multi-Critic SAC模型性能。
支持多回合评估、可视化、性能分析等功能。

使用方法:
    python scripts/evaluate.py --model models/final_model.pt --episodes 10
    python scripts/evaluate.py --model models/final_model.pt --render --episodes 5

作者: Multi-Critic SAC Team
"""

import argparse
import os
import sys
from pathlib import Path
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from multi_critic_sac.config import load_config
from multi_critic_sac.utils.common import setup_logger


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, model_path: str, config_path: str = None):
        """
        初始化评估器
        
        Args:
            model_path: 模型路径
            config_path: 配置文件路径
        """
        self.model_path = model_path
        self.config_path = config_path
        
        # 设置日志
        self.logger = setup_logger("ModelEvaluator")
        
        # 加载配置
        if config_path and os.path.exists(config_path):
            self.config = load_config(config_path)
        else:
            # 使用默认配置
            from multi_critic_sac.config import get_default_config
            self.config = get_default_config()
        
        # 创建环境
        self.env = NavigationEnv(
            max_episode_steps=self.config.environment.max_episode_steps,
            render_mode="human"
        )
        
        # 加载模型
        self.model = self._load_model()
        
        self.logger.info(f"评估器初始化完成，模型: {model_path}")
    
    def _load_model(self) -> MultiCriticSAC:
        """加载模型"""
        # 创建模型实例
        model = MultiCriticSAC(
            policy="MlpPolicy",
            env=self.env,
            verbose=0
        )
        
        # 加载权重
        model.load(self.model_path)
        
        self.logger.info("模型加载完成")
        return model
    
    def evaluate_episodes(self, n_episodes: int = 10, 
                         deterministic: bool = True,
                         render: bool = False) -> Dict[str, any]:
        """
        评估多个回合
        
        Args:
            n_episodes: 评估回合数
            deterministic: 是否使用确定性策略
            render: 是否渲染
            
        Returns:
            Dict[str, any]: 评估结果
        """
        self.logger.info(f"开始评估 {n_episodes} 个回合")
        
        episode_rewards = []
        episode_lengths = []
        success_count = 0
        collision_count = 0
        
        # 分解奖励统计
        obstacle_rewards = []
        guidance_rewards = []
        environment_rewards = []
        
        for episode in range(n_episodes):
            obs, _ = self.env.reset()
            episode_reward = 0
            episode_length = 0
            episode_obstacle_reward = 0
            episode_guidance_reward = 0
            episode_environment_reward = 0
            
            done = False
            while not done:
                # 预测动作
                action, _ = self.model.predict(obs, deterministic=deterministic)
                
                # 执行动作
                obs, reward, terminated, truncated, info = self.env.step(action)
                done = terminated or truncated
                
                # 累计奖励和长度
                episode_reward += reward
                episode_length += 1
                episode_obstacle_reward += info.get('obstacle_reward', 0)
                episode_guidance_reward += info.get('guidance_reward', 0)
                episode_environment_reward += info.get('environment_reward', 0)
                
                # 渲染
                if render:
                    self.env.render()
                
                # 检查成功和碰撞
                if terminated:
                    env_info = self.env.get_environment_info()
                    if env_info['distance_to_goal'] < self.env.goal_threshold:
                        success_count += 1
                    else:
                        collision_count += 1
            
            # 记录回合结果
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            obstacle_rewards.append(episode_obstacle_reward)
            guidance_rewards.append(episode_guidance_reward)
            environment_rewards.append(episode_environment_reward)
            
            self.logger.info(
                f"回合 {episode + 1}: 奖励={episode_reward:.2f}, "
                f"长度={episode_length}, 成功={terminated and success_count > len(episode_rewards) - 1}"
            )
        
        # 计算统计信息
        results = {
            'n_episodes': n_episodes,
            'mean_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'min_reward': np.min(episode_rewards),
            'max_reward': np.max(episode_rewards),
            'mean_length': np.mean(episode_lengths),
            'std_length': np.std(episode_lengths),
            'success_rate': success_count / n_episodes,
            'collision_rate': collision_count / n_episodes,
            'mean_obstacle_reward': np.mean(obstacle_rewards),
            'mean_guidance_reward': np.mean(guidance_rewards),
            'mean_environment_reward': np.mean(environment_rewards),
            'episode_rewards': episode_rewards,
            'episode_lengths': episode_lengths,
            'obstacle_rewards': obstacle_rewards,
            'guidance_rewards': guidance_rewards,
            'environment_rewards': environment_rewards
        }
        
        return results
    
    def analyze_performance(self, results: Dict[str, any]) -> None:
        """分析性能结果"""
        print("\n" + "="*50)
        print("性能分析结果")
        print("="*50)
        
        print(f"评估回合数: {results['n_episodes']}")
        print(f"平均奖励: {results['mean_reward']:.2f} ± {results['std_reward']:.2f}")
        print(f"奖励范围: [{results['min_reward']:.2f}, {results['max_reward']:.2f}]")
        print(f"平均回合长度: {results['mean_length']:.1f} ± {results['std_length']:.1f}")
        print(f"成功率: {results['success_rate']:.1%}")
        print(f"碰撞率: {results['collision_rate']:.1%}")
        
        print("\n分解奖励分析:")
        print(f"平均避障奖励: {results['mean_obstacle_reward']:.2f}")
        print(f"平均引导奖励: {results['mean_guidance_reward']:.2f}")
        print(f"平均环境奖励: {results['mean_environment_reward']:.2f}")
        
        # 计算奖励贡献比例
        total_decomposed = (results['mean_obstacle_reward'] + 
                          results['mean_guidance_reward'] + 
                          results['mean_environment_reward'])
        
        if abs(total_decomposed) > 1e-6:
            print("\n奖励贡献比例:")
            print(f"避障奖励占比: {results['mean_obstacle_reward']/total_decomposed:.1%}")
            print(f"引导奖励占比: {results['mean_guidance_reward']/total_decomposed:.1%}")
            print(f"环境奖励占比: {results['mean_environment_reward']/total_decomposed:.1%}")
    
    def plot_results(self, results: Dict[str, any], save_path: str = None) -> None:
        """绘制评估结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 回合奖励
        axes[0, 0].plot(results['episode_rewards'], 'b-', alpha=0.7)
        axes[0, 0].axhline(y=results['mean_reward'], color='r', linestyle='--', 
                          label=f'平均值: {results["mean_reward"]:.2f}')
        axes[0, 0].set_title('回合奖励')
        axes[0, 0].set_xlabel('回合')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 回合长度
        axes[0, 1].plot(results['episode_lengths'], 'g-', alpha=0.7)
        axes[0, 1].axhline(y=results['mean_length'], color='r', linestyle='--',
                          label=f'平均值: {results["mean_length"]:.1f}')
        axes[0, 1].set_title('回合长度')
        axes[0, 1].set_xlabel('回合')
        axes[0, 1].set_ylabel('步数')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 分解奖励对比
        episodes = range(1, len(results['obstacle_rewards']) + 1)
        axes[1, 0].plot(episodes, results['obstacle_rewards'], 'r-', label='避障奖励', alpha=0.7)
        axes[1, 0].plot(episodes, results['guidance_rewards'], 'g-', label='引导奖励', alpha=0.7)
        axes[1, 0].plot(episodes, results['environment_rewards'], 'b-', label='环境奖励', alpha=0.7)
        axes[1, 0].set_title('分解奖励对比')
        axes[1, 0].set_xlabel('回合')
        axes[1, 0].set_ylabel('奖励')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 奖励分布直方图
        axes[1, 1].hist(results['episode_rewards'], bins=10, alpha=0.7, color='blue', edgecolor='black')
        axes[1, 1].axvline(x=results['mean_reward'], color='r', linestyle='--', 
                          label=f'平均值: {results["mean_reward"]:.2f}')
        axes[1, 1].set_title('奖励分布')
        axes[1, 1].set_xlabel('奖励')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果图表已保存: {save_path}")
        
        plt.show()
    
    def save_results(self, results: Dict[str, any], save_path: str) -> None:
        """保存评估结果"""
        # 转换numpy数组为列表以便JSON序列化
        json_results = {}
        for key, value in results.items():
            if isinstance(value, np.ndarray):
                json_results[key] = value.tolist()
            elif isinstance(value, (np.integer, np.floating)):
                json_results[key] = float(value)
            else:
                json_results[key] = value
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"评估结果已保存: {save_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Multi-Critic SAC模型评估脚本")
    parser.add_argument(
        "--model", "-m",
        type=str,
        required=True,
        help="模型文件路径"
    )
    parser.add_argument(
        "--config", "-c",
        type=str,
        default=None,
        help="配置文件路径"
    )
    parser.add_argument(
        "--episodes", "-e",
        type=int,
        default=10,
        help="评估回合数"
    )
    parser.add_argument(
        "--render", "-r",
        action="store_true",
        help="是否渲染环境"
    )
    parser.add_argument(
        "--deterministic", "-d",
        action="store_true",
        default=True,
        help="是否使用确定性策略"
    )
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        default="evaluation_results",
        help="结果输出目录"
    )
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建评估器
    evaluator = ModelEvaluator(args.model, args.config)
    
    # 执行评估
    results = evaluator.evaluate_episodes(
        n_episodes=args.episodes,
        deterministic=args.deterministic,
        render=args.render
    )
    
    # 分析结果
    evaluator.analyze_performance(results)
    
    # 保存结果
    results_file = output_dir / "evaluation_results.json"
    evaluator.save_results(results, str(results_file))
    
    # 绘制图表
    plot_file = output_dir / "evaluation_plots.png"
    evaluator.plot_results(results, str(plot_file))


if __name__ == "__main__":
    main()
