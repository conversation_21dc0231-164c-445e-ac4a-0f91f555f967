#!/usr/bin/env python3
"""
Multi-Critic SAC演示脚本

快速演示Multi-Critic SAC算法的基本功能。
包括环境创建、模型训练、评估等基本流程。

使用方法:
    python scripts/demo.py

作者: Multi-Critic SAC Team
"""

import sys
from pathlib import Path
import numpy as np
import torch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from multi_critic_sac.config import get_default_config
from multi_critic_sac.utils.common import set_seed


def demo_environment():
    """演示环境功能"""
    print("="*50)
    print("环境演示")
    print("="*50)
    
    # 创建环境
    env = NavigationEnv(
        map_size=50.0,
        num_obstacles=5,
        max_episode_steps=200,
        render_mode="human"
    )
    
    print(f"环境信息:")
    print(f"  观测空间: {env.observation_space}")
    print(f"  动作空间: {env.action_space}")
    print(f"  状态维度: {env.observation_space.shape[0]}")
    print(f"  动作维度: {env.action_space.shape[0]}")
    
    # 重置环境
    obs, info = env.reset()
    print(f"\n初始观测形状: {obs.shape}")
    
    # 随机动作演示
    print("\n执行随机动作演示...")
    for step in range(10):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        
        print(f"步骤 {step+1}: 动作={action}, 奖励={reward:.3f}, "
              f"避障奖励={info.get('obstacle_reward', 0):.3f}, "
              f"引导奖励={info.get('guidance_reward', 0):.3f}, "
              f"环境奖励={info.get('environment_reward', 0):.3f}")
        
        if terminated or truncated:
            print("回合结束")
            break
    
    env.close()


def demo_model_creation():
    """演示模型创建"""
    print("\n" + "="*50)
    print("模型创建演示")
    print("="*50)
    
    # 创建环境
    env = NavigationEnv(max_episode_steps=100)
    
    # 获取默认配置
    config = get_default_config()
    
    # 创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        learning_rate=config.algorithm.learning_rate,
        batch_size=config.algorithm.batch_size,
        critic_weights=config.critic_weights.__dict__,
        verbose=1,
        config=config
    )
    
    print("模型创建成功!")
    print(f"模型参数:")
    print(f"  学习率: {model.learning_rate}")
    print(f"  批次大小: {model.batch_size}")
    print(f"  评论家权重: {model.critic_weights}")
    
    # 测试预测
    obs, _ = env.reset()
    action, _ = model.predict(obs, deterministic=True)
    print(f"\n测试预测:")
    print(f"  输入观测形状: {obs.shape}")
    print(f"  输出动作: {action}")
    print(f"  动作形状: {action.shape}")
    
    env.close()
    return model


def demo_short_training():
    """演示短期训练"""
    print("\n" + "="*50)
    print("短期训练演示")
    print("="*50)
    
    # 设置随机种子
    set_seed(42)
    
    # 创建环境
    env = NavigationEnv(
        map_size=30.0,
        num_obstacles=3,
        max_episode_steps=100
    )
    
    # 创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=env,
        learning_rate=3e-4,
        buffer_size=10000,
        learning_starts=100,
        batch_size=64,
        verbose=1
    )
    
    print("开始短期训练（1000步）...")
    
    # 训练模型
    model.learn(total_timesteps=1000, log_interval=200)
    
    print("训练完成!")
    
    # 获取训练统计
    stats = model.get_training_stats()
    print(f"训练统计:")
    print(f"  总步数: {stats['num_timesteps']}")
    print(f"  更新次数: {stats['n_updates']}")
    print(f"  缓冲区大小: {stats['buffer_size']}")
    
    env.close()
    return model


def demo_evaluation(model):
    """演示模型评估"""
    print("\n" + "="*50)
    print("模型评估演示")
    print("="*50)
    
    # 创建评估环境
    env = NavigationEnv(
        map_size=30.0,
        num_obstacles=3,
        max_episode_steps=100
    )
    
    # 评估模型
    n_eval_episodes = 3
    episode_rewards = []
    
    for episode in range(n_eval_episodes):
        obs, _ = env.reset()
        episode_reward = 0
        step_count = 0
        
        done = False
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            step_count += 1
        
        episode_rewards.append(episode_reward)
        
        print(f"评估回合 {episode+1}: 奖励={episode_reward:.2f}, 步数={step_count}")
        
        # 显示环境信息
        env_info = env.get_environment_info()
        print(f"  到目标距离: {env_info['distance_to_goal']:.2f}")
        print(f"  最近障碍物距离: {env_info['min_obstacle_distance']:.2f}")
    
    # 计算平均性能
    mean_reward = np.mean(episode_rewards)
    std_reward = np.std(episode_rewards)
    
    print(f"\n评估结果:")
    print(f"  平均奖励: {mean_reward:.2f} ± {std_reward:.2f}")
    print(f"  奖励范围: [{min(episode_rewards):.2f}, {max(episode_rewards):.2f}]")
    
    env.close()


def demo_config_system():
    """演示配置系统"""
    print("\n" + "="*50)
    print("配置系统演示")
    print("="*50)
    
    # 获取默认配置
    config = get_default_config()
    
    print("默认配置:")
    print(f"  算法学习率: {config.algorithm.learning_rate}")
    print(f"  批次大小: {config.algorithm.batch_size}")
    print(f"  评论家权重: {config.critic_weights.__dict__}")
    print(f"  环境配置: {config.environment.__dict__}")
    print(f"  训练配置: {config.training.__dict__}")
    
    # 修改配置
    config.algorithm.learning_rate = 1e-3
    config.critic_weights.obstacle = 0.5
    config.critic_weights.guidance = 0.3
    config.critic_weights.environment = 0.2
    
    print("\n修改后的配置:")
    print(f"  算法学习率: {config.algorithm.learning_rate}")
    print(f"  评论家权重: {config.critic_weights.__dict__}")
    
    # 验证配置
    from multi_critic_sac.config import validate_config
    try:
        validate_config(config)
        print("配置验证通过!")
    except ValueError as e:
        print(f"配置验证失败: {e}")


def main():
    """主演示函数"""
    print("Multi-Critic SAC 算法演示")
    print("作者: Multi-Critic SAC Team")
    print("版本: 1.0.0")
    
    try:
        # 1. 环境演示
        demo_environment()
        
        # 2. 模型创建演示
        model = demo_model_creation()
        
        # 3. 短期训练演示
        trained_model = demo_short_training()
        
        # 4. 模型评估演示
        demo_evaluation(trained_model)
        
        # 5. 配置系统演示
        demo_config_system()
        
        print("\n" + "="*50)
        print("演示完成!")
        print("="*50)
        print("\n下一步:")
        print("1. 使用 'python scripts/train.py --config configs/default.yaml' 进行完整训练")
        print("2. 使用 'python scripts/evaluate.py --model models/final_model.pt' 进行模型评估")
        print("3. 查看 README.md 了解更多使用方法")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
