"""
安装物理导航环境所需的依赖

自动检测系统环境并安装Pymunk物理引擎。
"""

import subprocess
import sys
import platform
import os


def check_pymunk_availability():
    """检查Pymunk是否已安装"""
    try:
        import pymunk
        print(f"✅ Pymunk已安装，版本: {pymunk.version}")
        return True
    except ImportError:
        print("❌ Pymunk未安装")
        return False


def install_pymunk():
    """安装Pymunk"""
    print("🔧 开始安装Pymunk...")
    
    try:
        # 使用pip安装pymunk
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pymunk"])
        print("✅ Pymunk安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Pymunk安装失败: {e}")
        return False


def install_optional_dependencies():
    """安装可选依赖"""
    optional_packages = [
        "pygame",  # 用于可视化渲染
        "matplotlib"  # 用于绘图和分析
    ]
    
    for package in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"🔧 安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"⚠️  {package} 安装失败（可选依赖）")


def verify_installation():
    """验证安装"""
    print("\n🧪 验证安装...")
    
    try:
        import pymunk
        
        # 创建简单的物理世界测试
        space = pymunk.Space()
        space.gravity = (0, -981)
        
        # 创建一个简单的物体
        body = pymunk.Body(1, pymunk.moment_for_circle(1, 0, 1))
        shape = pymunk.Circle(body, 1)
        space.add(body, shape)
        
        # 执行一步仿真
        space.step(0.01)
        
        print("✅ Pymunk功能验证成功")
        return True
        
    except Exception as e:
        print(f"❌ Pymunk功能验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 物理导航环境依赖安装器")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    
    # 检查当前安装状态
    if check_pymunk_availability():
        print("Pymunk已安装，跳过安装步骤")
    else:
        # 安装Pymunk
        if not install_pymunk():
            print("❌ 安装失败，请手动安装Pymunk")
            print("手动安装命令: pip install pymunk")
            return False
    
    # 安装可选依赖
    install_optional_dependencies()
    
    # 验证安装
    if verify_installation():
        print("\n🎉 所有依赖安装完成！")
        print("现在可以使用PhysicsNavigationEnv了。")
        
        # 提供使用示例
        print("\n📖 使用示例:")
        print("```python")
        print("from multi_critic_sac.envs import PhysicsNavigationEnv")
        print("")
        print("# 创建物理导航环境")
        print("env = PhysicsNavigationEnv(use_physics_engine=True)")
        print("obs, info = env.reset()")
        print("")
        print("# 执行动作")
        print("action = env.action_space.sample()")
        print("obs, reward, terminated, truncated, info = env.step(action)")
        print("```")
        
        return True
    else:
        print("\n❌ 安装验证失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
