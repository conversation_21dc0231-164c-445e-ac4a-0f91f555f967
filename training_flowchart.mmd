graph TD
    A[开始训练] --> B[初始化NavigationEnv环境]
    B --> C[初始化Multi-Critic SAC算法]
    C --> D[创建Actor网络]
    C --> E[创建三个专家网络]
    C --> F[创建融合模块FusionModule]
    C --> G[创建经验回放缓冲区ReplayBuffer]
    
    E --> E1[避障专家<br/>ObstacleExpert<br/>专注Q值估计]
    E --> E2[引导专家<br/>GuidanceExpert<br/>专注Q值估计]
    E --> E3[环境影响专家<br/>EnvironmentExpert<br/>专注Q值估计]
    
    %% 关键修复：添加从初始化到训练循环的连接
    D --> H[开始主训练循环]
    F --> H
    G --> H
    E1 --> H
    E2 --> H
    E3 --> H
    
    H --> I{是否达到学习开始步数?}
    I -->|否| J[随机动作探索]
    I -->|是| K[Actor网络预测动作]
    
    J --> L[执行动作action]
    K --> L
    L --> M[环境step方法]
    
    M --> N[环境内部奖励计算]
    N --> N1[计算避障奖励<br/>obstacle_reward]
    N --> N2[计算引导奖励<br/>guidance_reward]
    N --> N3[计算环境影响奖励<br/>environment_reward]
    N --> N4[计算总奖励<br/>total_reward]
    
    N1 --> O[构建info字典]
    N2 --> O
    N3 --> O
    N4 --> O
    
    O --> P[返回: next_state, total_reward, done, info]
    P --> Q[存储完整经验到ReplayBuffer<br/>包含分解奖励]
    
    Q --> R{是否达到训练频率?}
    R -->|否| S[继续收集经验]
    R -->|是| T[开始训练步骤]
    
    T --> U[从ReplayBuffer采样批次数据]
    U --> V[批次数据包含分解奖励]
    V --> V1[states, actions, next_states, dones]
    V --> V2[obstacle_rewards]
    V --> V3[guidance_rewards] 
    V --> V4[environment_rewards]
    
    V1 --> W[并行更新三个专家网络]
    V2 --> W1[避障专家更新<br/>使用obstacle_rewards<br/>计算Q值损失]
    V3 --> W2[引导专家更新<br/>使用guidance_rewards<br/>计算Q值损失]
    V4 --> W3[环境专家更新<br/>使用environment_rewards<br/>计算Q值损失]
    
    W --> W1
    W --> W2
    W --> W3
    
    W1 --> X[融合模块整合Q值]
    W2 --> X
    W3 --> X
    
    X --> X1[获取各专家Q值]
    X1 --> X2[加权融合策略<br/>obstacle: 40%<br/>guidance: 40%<br/>environment: 20%]
    X2 --> X3[输出融合Q值]
    
    X3 --> Y[更新Actor网络]
    Y --> Y1[Actor采样新动作]
    Y1 --> Y2[计算Actor损失<br/>使用融合Q值]
    Y2 --> Y3[Actor梯度更新]
    
    Y3 --> Z[更新熵系数α]
    Z --> AA[软更新目标网络]
    AA --> AA1[更新避障专家目标网络]
    AA --> AA2[更新引导专家目标网络]
    AA --> AA3[更新环境专家目标网络]
    
    AA1 --> BB{是否完成训练?}
    AA2 --> BB
    AA3 --> BB
    
    BB -->|否| S
    BB -->|是| CC[保存模型]
    
    S --> I
    CC --> DD[训练结束]
    
    style E1 fill:#ffcccc
    style E2 fill:#ccffcc  
    style E3 fill:#ccccff
    style N fill:#fff2cc
    style X fill:#e1d5e7
    style Y fill:#d5e8d4
    style H fill:#dae8fc
    
    classDef rewardCalc fill:#fff2cc,stroke:#d6b656,stroke-width:2px
    classDef expertUpdate fill:#f8cecc,stroke:#b85450,stroke-width:2px
    classDef fusion fill:#e1d5e7,stroke:#9673a6,stroke-width:2px
    classDef actor fill:#d5e8d4,stroke:#82b366,stroke-width:2px
    classDef mainLoop fill:#dae8fc,stroke:#6c8ebf,stroke-width:3px
    
    class N,N1,N2,N3,N4 rewardCalc
    class W1,W2,W3 expertUpdate
    class X,X1,X2,X3 fusion
    class Y,Y1,Y2,Y3 actor
    class H mainLoop