# Project Structure

This document defines the organization and folder structure conventions for the project.

## Root Directory
```
/
├── src/              # Source code
├── tests/            # Test files
├── docs/             # Documentation
├── config/           # Configuration files
├── scripts/          # Build and utility scripts
├── assets/           # Static assets (images, fonts, etc.)
└── .kiro/            # Kiro AI assistant configuration
    └── steering/     # AI guidance documents
```

## Source Code Organization
- **src/**: Main application source code
  - Keep modules focused and single-purpose
  - Use clear, descriptive naming conventions
  - Group related functionality together

## File Naming Conventions
- Use consistent naming patterns across the project
- Prefer kebab-case for file names (e.g., `user-service.js`)
- Use descriptive names that indicate purpose
- Include file type suffixes when helpful (e.g., `.test.js`, `.config.js`)

## Directory Guidelines
- **Flat structure**: Avoid deep nesting when possible
- **Logical grouping**: Group files by feature or domain, not by file type
- **Clear separation**: Keep source code, tests, and configuration separate
- **Documentation**: Include README files in complex directories

## Import/Export Patterns
- Use absolute imports from project root when possible
- Keep import statements organized and grouped
- Export only what needs to be public
- Use index files to create clean module boundaries

## Configuration Files
- Keep configuration at project root or in dedicated config/ directory
- Use environment-specific config files when needed
- Document configuration options and their purposes

*Adapt this structure as the project grows and requirements become clearer.*