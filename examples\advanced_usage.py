#!/usr/bin/env python3
"""
Multi-Critic SAC高级使用示例

演示Multi-Critic SAC算法的高级功能，包括：
- 自定义环境
- 高级配置
- 多进程训练
- 模型微调
- 性能优化

作者: Multi-Critic SAC Team
"""

import sys
from pathlib import Path
import numpy as np
import torch
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.monitor import Monitor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from multi_critic_sac import MultiCriticSAC
from multi_critic_sac.envs import NavigationEnv
from multi_critic_sac.config import get_default_config, Config
from multi_critic_sac.utils.common import set_seed, create_directories
from multi_critic_sac.utils.visualization import TrainingVisualizer


class CustomNavigationEnv(NavigationEnv):
    """自定义导航环境示例"""
    
    def __init__(self, difficulty="normal", **kwargs):
        """
        初始化自定义环境
        
        Args:
            difficulty: 难度级别 ("easy", "normal", "hard")
        """
        # 根据难度设置参数
        if difficulty == "easy":
            kwargs.setdefault("map_size", 30.0)
            kwargs.setdefault("num_obstacles", 2)
            kwargs.setdefault("max_episode_steps", 150)
        elif difficulty == "normal":
            kwargs.setdefault("map_size", 50.0)
            kwargs.setdefault("num_obstacles", 5)
            kwargs.setdefault("max_episode_steps", 200)
        elif difficulty == "hard":
            kwargs.setdefault("map_size", 80.0)
            kwargs.setdefault("num_obstacles", 10)
            kwargs.setdefault("max_episode_steps", 300)
        
        super().__init__(**kwargs)
        self.difficulty = difficulty
    
    def _calculate_guidance_reward(self) -> float:
        """重写引导奖励计算"""
        base_reward = super()._calculate_guidance_reward()
        
        # 根据难度调整奖励
        if self.difficulty == "hard":
            # 困难模式下增加到达目标的奖励
            distance_to_goal = self.get_environment_info()['distance_to_goal']
            if distance_to_goal < self.goal_threshold:
                base_reward += 50.0  # 额外奖励
        
        return base_reward


def multiprocess_training_example():
    """多进程训练示例"""
    print("="*60)
    print("多进程训练示例")
    print("="*60)
    
    def make_env(rank, seed=0):
        """环境创建函数"""
        def _init():
            env = CustomNavigationEnv(difficulty="normal", seed=seed + rank)
            env = Monitor(env)
            return env
        return _init
    
    # 创建多进程环境
    n_envs = 4
    env_fns = [make_env(i, seed=42) for i in range(n_envs)]
    vec_env = SubprocVecEnv(env_fns)
    
    print(f"创建了 {n_envs} 个并行环境")
    
    # 创建模型
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=vec_env,
        learning_rate=3e-4,
        buffer_size=100000,
        learning_starts=1000,
        batch_size=256,
        verbose=1
    )
    
    print("开始多进程训练...")
    
    # 训练
    model.learn(total_timesteps=20000, log_interval=2000)
    
    print("多进程训练完成!")
    
    # 保存模型
    create_directories(["models"])
    model.save("models/multiprocess_model.pt")
    
    vec_env.close()
    return model


def adaptive_weights_example():
    """自适应权重示例"""
    print("\n" + "="*60)
    print("自适应权重示例")
    print("="*60)
    
    # 创建环境
    env = CustomNavigationEnv(difficulty="normal")
    
    # 创建带自适应权重的融合模块
    from multi_critic_sac.critics.fusion_module import FusionModule
    from multi_critic_sac.critics.obstacle_expert import ObstacleExpert
    from multi_critic_sac.critics.guidance_expert import GuidanceExpert
    from multi_critic_sac.critics.environment_expert import EnvironmentExpert
    
    # 获取环境信息
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建专家
    obstacle_expert = ObstacleExpert(state_dim, action_dim, device=device)
    guidance_expert = GuidanceExpert(state_dim, action_dim, device=device)
    environment_expert = EnvironmentExpert(state_dim, action_dim, device=device)
    
    # 创建自适应融合模块
    fusion_module = FusionModule(
        obstacle_expert=obstacle_expert,
        guidance_expert=guidance_expert,
        environment_expert=environment_expert,
        fusion_strategy="weighted_sum",
        adaptive_weights=True,
        device=device
    )
    
    print("创建了自适应权重融合模块")
    
    # 测试权重变化
    obs, _ = env.reset()
    action = env.action_space.sample()
    
    obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(device)
    action_tensor = torch.FloatTensor(action).unsqueeze(0).to(device)
    
    weights = fusion_module.get_fusion_weights(obs_tensor, action_tensor)
    print(f"当前权重: 避障={weights['obstacle'].item():.3f}, "
          f"引导={weights['guidance'].item():.3f}, "
          f"环境={weights['environment'].item():.3f}")
    
    env.close()


def curriculum_learning_example():
    """课程学习示例"""
    print("\n" + "="*60)
    print("课程学习示例")
    print("="*60)
    
    # 阶段1：简单环境
    print("阶段1: 简单环境训练")
    easy_env = CustomNavigationEnv(difficulty="easy")
    
    model = MultiCriticSAC(
        policy="MlpPolicy",
        env=easy_env,
        learning_rate=3e-4,
        learning_starts=500,
        verbose=1
    )
    
    # 在简单环境中训练
    model.learn(total_timesteps=5000, log_interval=1000)
    print("简单环境训练完成")
    
    # 阶段2：中等环境
    print("\n阶段2: 中等环境训练")
    normal_env = CustomNavigationEnv(difficulty="normal")
    
    # 更新环境
    model.set_env(normal_env)
    
    # 继续训练
    model.learn(total_timesteps=8000, log_interval=2000)
    print("中等环境训练完成")
    
    # 阶段3：困难环境
    print("\n阶段3: 困难环境训练")
    hard_env = CustomNavigationEnv(difficulty="hard")
    
    # 更新环境
    model.set_env(hard_env)
    
    # 最终训练
    model.learn(total_timesteps=10000, log_interval=2000)
    print("困难环境训练完成")
    
    # 保存最终模型
    create_directories(["models"])
    model.save("models/curriculum_model.pt")
    
    # 在所有难度下评估
    print("\n课程学习评估:")
    for difficulty in ["easy", "normal", "hard"]:
        print(f"\n{difficulty.upper()}难度评估:")
        test_env = CustomNavigationEnv(difficulty=difficulty)
        evaluate_on_env(model, test_env, n_episodes=3)
        test_env.close()
    
    easy_env.close()
    normal_env.close()
    hard_env.close()


def evaluate_on_env(model, env, n_episodes=5):
    """在指定环境上评估模型"""
    episode_rewards = []
    success_count = 0
    
    for episode in range(n_episodes):
        obs, _ = env.reset()
        episode_reward = 0
        
        done = False
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            episode_reward += reward
        
        episode_rewards.append(episode_reward)
        
        # 检查成功
        env_info = env.get_environment_info()
        if env_info['distance_to_goal'] < env.goal_threshold:
            success_count += 1
    
    mean_reward = np.mean(episode_rewards)
    success_rate = success_count / n_episodes
    
    print(f"  平均奖励: {mean_reward:.2f}")
    print(f"  成功率: {success_rate:.1%}")


def hyperparameter_tuning_example():
    """超参数调优示例"""
    print("\n" + "="*60)
    print("超参数调优示例")
    print("="*60)
    
    # 定义超参数组合
    hyperparams = [
        {"learning_rate": 1e-4, "batch_size": 128, "tau": 0.005},
        {"learning_rate": 3e-4, "batch_size": 256, "tau": 0.01},
        {"learning_rate": 1e-3, "batch_size": 64, "tau": 0.001},
    ]
    
    results = []
    
    for i, params in enumerate(hyperparams):
        print(f"\n测试超参数组合 {i+1}: {params}")
        
        # 创建环境
        env = CustomNavigationEnv(difficulty="normal")
        
        # 创建模型
        model = MultiCriticSAC(
            policy="MlpPolicy",
            env=env,
            learning_rate=params["learning_rate"],
            batch_size=params["batch_size"],
            tau=params["tau"],
            learning_starts=500,
            verbose=0
        )
        
        # 训练
        model.learn(total_timesteps=5000)
        
        # 评估
        episode_rewards = []
        for _ in range(5):
            obs, _ = env.reset()
            episode_reward = 0
            
            done = False
            while not done:
                action, _ = model.predict(obs, deterministic=True)
                obs, reward, terminated, truncated, info = env.step(action)
                done = terminated or truncated
                episode_reward += reward
            
            episode_rewards.append(episode_reward)
        
        mean_reward = np.mean(episode_rewards)
        results.append((params, mean_reward))
        
        print(f"  平均奖励: {mean_reward:.2f}")
        
        env.close()
    
    # 找到最佳超参数
    best_params, best_reward = max(results, key=lambda x: x[1])
    print(f"\n最佳超参数: {best_params}")
    print(f"最佳性能: {best_reward:.2f}")


def transfer_learning_example():
    """迁移学习示例"""
    print("\n" + "="*60)
    print("迁移学习示例")
    print("="*60)
    
    # 源任务：简单环境
    print("源任务训练（简单环境）...")
    source_env = CustomNavigationEnv(difficulty="easy")
    
    source_model = MultiCriticSAC(
        policy="MlpPolicy",
        env=source_env,
        learning_starts=500,
        verbose=1
    )
    
    # 训练源模型
    source_model.learn(total_timesteps=8000, log_interval=2000)
    
    # 保存源模型
    create_directories(["models"])
    source_model.save("models/source_model.pt")
    print("源模型训练完成并保存")
    
    # 目标任务：困难环境
    print("\n目标任务训练（困难环境）...")
    target_env = CustomNavigationEnv(difficulty="hard")
    
    # 创建目标模型并加载源模型权重
    target_model = MultiCriticSAC(
        policy="MlpPolicy",
        env=target_env,
        learning_starts=200,  # 减少探索，利用预训练知识
        verbose=1
    )
    
    # 加载源模型权重
    target_model.load("models/source_model.pt")
    print("已加载源模型权重")
    
    # 在目标任务上微调
    target_model.learn(total_timesteps=5000, log_interval=1000)
    
    # 保存微调后的模型
    target_model.save("models/transfer_model.pt")
    print("迁移学习完成")
    
    # 对比评估
    print("\n迁移学习效果对比:")
    
    # 从头训练的模型
    print("从头训练模型:")
    scratch_model = MultiCriticSAC(
        policy="MlpPolicy",
        env=target_env,
        learning_starts=500,
        verbose=0
    )
    scratch_model.learn(total_timesteps=5000)
    evaluate_on_env(scratch_model, target_env, n_episodes=3)
    
    # 迁移学习模型
    print("\n迁移学习模型:")
    evaluate_on_env(target_model, target_env, n_episodes=3)
    
    source_env.close()
    target_env.close()


def main():
    """主函数"""
    print("Multi-Critic SAC 高级使用示例")
    print("作者: Multi-Critic SAC Team")
    
    try:
        # 1. 多进程训练
        multiprocess_training_example()
        
        # 2. 自适应权重
        adaptive_weights_example()
        
        # 3. 课程学习
        curriculum_learning_example()
        
        # 4. 超参数调优
        hyperparameter_tuning_example()
        
        # 5. 迁移学习
        transfer_learning_example()
        
        print("\n" + "="*60)
        print("所有高级示例运行完成!")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n示例被用户中断")
    except Exception as e:
        print(f"\n示例运行中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
